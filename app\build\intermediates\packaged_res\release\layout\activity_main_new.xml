<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- الخلفية الرئيسية الجديدة -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/lightGray">

        <!-- المحتوى القابل للتمرير -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/bottomNavigationContainer"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="16dp">

                <!-- الهيدر العلوي -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp"
                    android:background="@color/lightGray">

                    <!-- أيقونة القائمة -->
                    <ImageView
                        android:id="@+id/menuIcon"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/ic_menu"
                        android:contentDescription="@string/menu_description"
                        app:tint="@color/darkText" />

                    <!-- العنوان الرئيسي -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="الرئيسية"
                        android:textSize="18sp"
                        android:textColor="@color/headerTextColor"
                        android:textStyle="bold"
                        android:fontFamily="@font/noto" />

                    <!-- أيقونات الإشعارات والوضع الليلي -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/darkModeIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="12dp"
                            android:src="@drawable/ic_dark_mode"
                            app:tint="@color/darkText" />

                        <ImageView
                            android:id="@+id/notificationIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_notifications"
                            app:tint="@color/darkText" />

                    </LinearLayout>

                </RelativeLayout>

                <!-- بطاقة السبت -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="180dp"
                    android:layout_margin="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/saturday_card_background"
                        android:padding="20dp">

                        <!-- نص السبت -->
                        <TextView
                            android:id="@+id/dayText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_centerHorizontal="true"
                            android:text="السبت"
                            android:textSize="24sp"
                            android:textColor="@color/whiteText"
                            android:textStyle="bold"
                            android:fontFamily="@font/noto" />

                        <!-- الوقت الحالي -->
                        <TextView
                            android:id="@+id/currentTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="01:04 ص"
                            android:textSize="32sp"
                            android:textColor="@color/whiteText"
                            android:textStyle="bold"
                            android:fontFamily="@font/noto" />

                        <!-- آخر قراءة -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:layout_alignParentStart="true"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="آخر قراءة"
                                android:textSize="14sp"
                                android:textColor="@color/whiteText"
                                android:fontFamily="@font/noto" />

                            <TextView
                                android:id="@+id/lastReadSurah"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="سورة الفاتحة"
                                android:textSize="16sp"
                                android:textColor="@color/whiteText"
                                android:textStyle="bold"
                                android:fontFamily="@font/surahname" />

                        </LinearLayout>



                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

                <!-- أوقات الصلاة -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:weightSum="5">

                    <!-- الفجر -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="5:46"
                            android:textSize="14sp"
                            android:textColor="@color/darkText"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الفجر"
                            android:textSize="12sp"
                            android:textColor="@color/darkText"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- الظهر -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="12:26"
                            android:textSize="14sp"
                            android:textColor="@color/darkText"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الظهر"
                            android:textSize="12sp"
                            android:textColor="@color/darkText"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- العصر -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="3:46"
                            android:textSize="14sp"
                            android:textColor="@color/darkText"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="العصر"
                            android:textSize="12sp"
                            android:textColor="@color/darkText"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- المغرب -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="7:11"
                            android:textSize="14sp"
                            android:textColor="@color/darkText"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="المغرب"
                            android:textSize="12sp"
                            android:textColor="@color/darkText"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- العشاء -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="8:30"
                            android:textSize="14sp"
                            android:textColor="@color/darkText"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="العشاء"
                            android:textSize="12sp"
                            android:textColor="@color/darkText"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                </LinearLayout>

                <!-- عنوان الأقسام الرئيسية -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="الأقسام الرئيسية"
                        android:textSize="18sp"
                        android:textColor="@color/darkText"
                        android:textStyle="bold"
                        android:fontFamily="@font/noto" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تحديث المواقيت ⟲"
                        android:textSize="12sp"
                        android:textColor="@color/purplePrimary"
                        android:fontFamily="@font/noto" />

                </LinearLayout>

                <!-- الصف الأول من الأقسام -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <!-- القرآن الكريم -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btnQuranSuras"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="6dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/section_card_bg"
                            android:padding="16dp">

                            <ImageView
                                android:id="@+id/quranSectionIcon"
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:src="@drawable/qurancover"
                                app:tint="@color/purplePrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentStart="true"
                                android:text="القرآن الكريم"
                                android:textSize="16sp"
                                android:textColor="@color/darkText"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- أذكار -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btnAzkar"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="6dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/section_card_bg"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:src="@drawable/ic_azkar"
                                app:tint="@color/purplePrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentStart="true"
                                android:text="أذكار"
                                android:textSize="16sp"
                                android:textColor="@color/darkText"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <!-- الصف الثاني من الأقسام -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <!-- أدعية -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btnDua"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="6dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/section_card_bg"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:src="@drawable/ic_dua_hands"
                                app:tint="@color/purplePrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentStart="true"
                                android:text="أدعية"
                                android:textSize="16sp"
                                android:textColor="@color/darkText"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                    <!-- مواقيت الصلاة -->
                    <androidx.cardview.widget.CardView
                        android:id="@+id/btnPrayerTimes"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="6dp"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/section_card_bg"
                            android:padding="16dp">

                            <ImageView
                                android:layout_width="40dp"
                                android:layout_height="40dp"
                                android:layout_alignParentTop="true"
                                android:layout_alignParentEnd="true"
                                android:src="@drawable/ic_prayer_time"
                                app:tint="@color/purplePrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentBottom="true"
                                android:layout_alignParentStart="true"
                                android:text="مواقيت الصلاة"
                                android:textSize="16sp"
                                android:textColor="@color/darkText"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </RelativeLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

                <!-- اتجاه القبلة -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/btnQibla"
                    android:layout_width="match_parent"
                    android:layout_height="80dp"
                    android:layout_marginHorizontal="16dp"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="4dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/section_card_bg"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:src="@drawable/ic_qibla"
                            app:tint="@color/purplePrimary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:text="اتجاه القبلة"
                            android:textSize="18sp"
                            android:textColor="@color/darkText"
                            android:textStyle="bold"
                            android:fontFamily="@font/noto" />

                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </ScrollView>

        <!-- شريط التنقل السفلي العصري الجديد -->
        <RelativeLayout
            android:id="@+id/bottomNavigationContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@android:color/transparent"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp"
            android:paddingTop="8dp">

            <!-- الخلفية العصرية المحدثة للشريط -->
            <LinearLayout
                android:id="@+id/bottomNavigation"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:background="@drawable/modern_bottom_nav_bg"
                android:elevation="16dp"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <!-- أيقونة الرئيسية -->
                <LinearLayout
                    android:id="@+id/homeNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="4dp">

                    <ImageView
                        android:id="@+id/homeIcon"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/nav_home"
                        android:tint="@color/islamicGreenMedium"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الرئيسية"
                        android:textSize="10sp"
                        android:textColor="@color/islamicGreenMedium"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <!-- أيقونة القرآن -->
                <LinearLayout
                    android:id="@+id/quranNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="4dp">

                    <ImageView
                        android:id="@+id/quranIcon"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/nav_quran"
                        android:tint="@color/textSecondary"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="القرآن"
                        android:textSize="10sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <!-- أيقونة الاستماع -->
                <LinearLayout
                    android:id="@+id/listenNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="4dp">

                    <ImageView
                        android:id="@+id/listenIcon"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/nav_listen"
                        android:tint="@color/textSecondary"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الاستماع"
                        android:textSize="10sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <!-- أيقونة الأذكار -->
                <LinearLayout
                    android:id="@+id/azkarNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="4dp">

                    <ImageView
                        android:id="@+id/azkarIcon"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/nav_azkar"
                        android:tint="@color/textSecondary"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الأذكار"
                        android:textSize="10sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

                <!-- أيقونة المزيد -->
                <LinearLayout
                    android:id="@+id/moreNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="4dp">

                    <ImageView
                        android:id="@+id/moreIcon"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/nav_more"
                        android:tint="@color/textSecondary"
                        android:scaleType="centerInside" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المزيد"
                        android:textSize="10sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp" />

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>

    <!-- قائمة التنقل الجانبية -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="false"
        android:background="@color/backgroundSecondary"
        app:headerLayout="@layout/nav_header_quran"
        app:menu="@menu/activity_quran_drawer"
        app:itemTextColor="@color/textPrimary"
        app:itemIconTint="@color/islamicGreenMedium" />

</androidx.drawerlayout.widget.DrawerLayout>
