<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="content_azkar" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\content_azkar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView" rootNodeViewId="@+id/mainScrollView"><Targets><Target id="@+id/mainScrollView" tag="layout/content_azkar_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="319" endOffset="12"/></Target><Target id="@+id/notificationIcon" view="ImageView"><Expressions/><location startLine="37" startOffset="16" endLine="44" endOffset="74"/></Target><Target id="@+id/notificationStatusIndicator" view="View"><Expressions/><location startLine="47" startOffset="16" endLine="56" endOffset="50"/></Target><Target id="@+id/azkarTitle" view="TextView"><Expressions/><location startLine="61" startOffset="12" endLine="71" endOffset="42"/></Target><Target id="@+id/nightDayToggle" view="ImageView"><Expressions/><location startLine="74" startOffset="12" endLine="82" endOffset="60"/></Target><Target id="@+id/morningAzkarCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="97" startOffset="12" endLine="138" endOffset="47"/></Target><Target id="@+id/eveningAzkarCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="141" startOffset="12" endLine="182" endOffset="47"/></Target><Target id="@+id/wakeAzkarCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="185" startOffset="12" endLine="226" endOffset="47"/></Target><Target id="@+id/sleepAzkarCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="229" startOffset="12" endLine="270" endOffset="47"/></Target><Target id="@+id/prayerAzkarCard" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="273" startOffset="12" endLine="313" endOffset="47"/></Target></Targets></Layout>