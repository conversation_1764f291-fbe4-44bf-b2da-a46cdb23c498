// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAchievementBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView achievementCardView;

  @NonNull
  public final TextView achievementDescription;

  @NonNull
  public final ImageView achievementIcon;

  @NonNull
  public final ProgressBar achievementProgressBar;

  @NonNull
  public final TextView achievementTitle;

  @NonNull
  public final ImageView lockIcon;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final TextView rewardPoints;

  @NonNull
  public final ImageView statusIcon;

  @NonNull
  public final TextView unlockDate;

  private ItemAchievementBinding(@NonNull CardView rootView, @NonNull CardView achievementCardView,
      @NonNull TextView achievementDescription, @NonNull ImageView achievementIcon,
      @NonNull ProgressBar achievementProgressBar, @NonNull TextView achievementTitle,
      @NonNull ImageView lockIcon, @NonNull TextView progressText, @NonNull TextView rewardPoints,
      @NonNull ImageView statusIcon, @NonNull TextView unlockDate) {
    this.rootView = rootView;
    this.achievementCardView = achievementCardView;
    this.achievementDescription = achievementDescription;
    this.achievementIcon = achievementIcon;
    this.achievementProgressBar = achievementProgressBar;
    this.achievementTitle = achievementTitle;
    this.lockIcon = lockIcon;
    this.progressText = progressText;
    this.rewardPoints = rewardPoints;
    this.statusIcon = statusIcon;
    this.unlockDate = unlockDate;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAchievementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAchievementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_achievement, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAchievementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView achievementCardView = (CardView) rootView;

      id = R.id.achievementDescription;
      TextView achievementDescription = ViewBindings.findChildViewById(rootView, id);
      if (achievementDescription == null) {
        break missingId;
      }

      id = R.id.achievementIcon;
      ImageView achievementIcon = ViewBindings.findChildViewById(rootView, id);
      if (achievementIcon == null) {
        break missingId;
      }

      id = R.id.achievementProgressBar;
      ProgressBar achievementProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (achievementProgressBar == null) {
        break missingId;
      }

      id = R.id.achievementTitle;
      TextView achievementTitle = ViewBindings.findChildViewById(rootView, id);
      if (achievementTitle == null) {
        break missingId;
      }

      id = R.id.lockIcon;
      ImageView lockIcon = ViewBindings.findChildViewById(rootView, id);
      if (lockIcon == null) {
        break missingId;
      }

      id = R.id.progressText;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.rewardPoints;
      TextView rewardPoints = ViewBindings.findChildViewById(rootView, id);
      if (rewardPoints == null) {
        break missingId;
      }

      id = R.id.statusIcon;
      ImageView statusIcon = ViewBindings.findChildViewById(rootView, id);
      if (statusIcon == null) {
        break missingId;
      }

      id = R.id.unlockDate;
      TextView unlockDate = ViewBindings.findChildViewById(rootView, id);
      if (unlockDate == null) {
        break missingId;
      }

      return new ItemAchievementBinding((CardView) rootView, achievementCardView,
          achievementDescription, achievementIcon, achievementProgressBar, achievementTitle,
          lockIcon, progressText, rewardPoints, statusIcon, unlockDate);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
