// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAzkarBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final CardView azkarCardView;

  @NonNull
  public final TextView azkarContent;

  @NonNull
  public final TextView azkarCount;

  @NonNull
  public final ProgressBar azkarProgressBar;

  @NonNull
  public final TextView azkarStatus;

  @NonNull
  public final TextView azkarTitle;

  @NonNull
  public final Button incrementButton;

  @NonNull
  public final Button resetButton;

  private ItemAzkarBinding(@NonNull CardView rootView, @NonNull CardView azkarCardView,
      @NonNull TextView azkarContent, @NonNull TextView azkarCount,
      @NonNull ProgressBar azkarProgressBar, @NonNull TextView azkarStatus,
      @NonNull TextView azkarTitle, @NonNull Button incrementButton, @NonNull Button resetButton) {
    this.rootView = rootView;
    this.azkarCardView = azkarCardView;
    this.azkarContent = azkarContent;
    this.azkarCount = azkarCount;
    this.azkarProgressBar = azkarProgressBar;
    this.azkarStatus = azkarStatus;
    this.azkarTitle = azkarTitle;
    this.incrementButton = incrementButton;
    this.resetButton = resetButton;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAzkarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAzkarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_azkar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAzkarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      CardView azkarCardView = (CardView) rootView;

      id = R.id.azkarContent;
      TextView azkarContent = ViewBindings.findChildViewById(rootView, id);
      if (azkarContent == null) {
        break missingId;
      }

      id = R.id.azkarCount;
      TextView azkarCount = ViewBindings.findChildViewById(rootView, id);
      if (azkarCount == null) {
        break missingId;
      }

      id = R.id.azkarProgressBar;
      ProgressBar azkarProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (azkarProgressBar == null) {
        break missingId;
      }

      id = R.id.azkarStatus;
      TextView azkarStatus = ViewBindings.findChildViewById(rootView, id);
      if (azkarStatus == null) {
        break missingId;
      }

      id = R.id.azkarTitle;
      TextView azkarTitle = ViewBindings.findChildViewById(rootView, id);
      if (azkarTitle == null) {
        break missingId;
      }

      id = R.id.incrementButton;
      Button incrementButton = ViewBindings.findChildViewById(rootView, id);
      if (incrementButton == null) {
        break missingId;
      }

      id = R.id.resetButton;
      Button resetButton = ViewBindings.findChildViewById(rootView, id);
      if (resetButton == null) {
        break missingId;
      }

      return new ItemAzkarBinding((CardView) rootView, azkarCardView, azkarContent, azkarCount,
          azkarProgressBar, azkarStatus, azkarTitle, incrementButton, resetButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
