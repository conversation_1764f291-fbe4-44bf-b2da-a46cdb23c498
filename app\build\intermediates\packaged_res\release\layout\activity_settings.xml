<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="com.qurany2019.quranyapp.SettingsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Theme.QuranyApp.AppBarOverlay"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/Theme.QuranyApp.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/backgroundPrimary"
        app:layout_constraintTop_toBottomOf="@id/app_bar"
        app:layout_constraintBottom_toTopOf="@id/adView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- قسم المظهر والوضع الليلي الجديد والجميل -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp"
                app:cardBackgroundColor="@color/backgroundCard">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- عنوان القسم الجميل -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="24dp">

                        <ImageView
                            android:layout_width="28dp"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_palette"
                            android:tint="@color/islamicGreenMedium"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/appearance_settings"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:textColor="@color/textPrimary"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- بطاقة الوضع الليلي الجميلة -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="16dp"
                        app:cardElevation="4dp"
                        app:cardBackgroundColor="@color/settingsDarkModeCardBg">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:padding="20dp">

                            <!-- أيقونة الوضع الليلي الجميلة -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="16dp"
                                app:cardCornerRadius="25dp"
                                app:cardElevation="4dp"
                                app:cardBackgroundColor="@color/settingsDarkModeIconBg">

                                <ImageView
                                    android:id="@+id/darkModeIcon"
                                    android:layout_width="24dp"
                                    android:layout_height="24dp"
                                    android:src="@drawable/ic_moon"
                                    android:tint="@color/settingsDarkModeIconTint"
                                    android:layout_margin="12dp" />

                            </androidx.cardview.widget.CardView>

                            <!-- النصوص -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/darkModeTitle"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/dark_mode_title"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/textPrimary"
                                    android:fontFamily="@font/noto" />

                                <TextView
                                    android:id="@+id/darkModeDesc"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/dark_mode_desc"
                                    android:textSize="14sp"
                                    android:textColor="@color/textSecondary"
                                    android:fontFamily="@font/noto"
                                    android:layout_marginTop="4dp" />

                            </LinearLayout>

                            <!-- Switch جميل -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                app:cardCornerRadius="15dp"
                                app:cardElevation="2dp"
                                app:cardBackgroundColor="@color/switchCardBg">

                                <Switch
                                    android:id="@+id/switchDarkMode"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_margin="4dp"
                                    android:checked="false"
                                    android:thumbTint="@color/switchThumbColor"
                                    android:trackTint="@color/switchTrackColor" />

                            </androidx.cardview.widget.CardView>

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- قسم اللغة -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:background="@color/white">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/language_settings"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamicGreenDark"
                        android:layout_marginBottom="20dp"
                        android:drawableStart="@drawable/ic_language"
                        android:drawablePadding="12dp"
                        android:gravity="center_vertical" />

                    <!-- خيار اللغة العربية -->
                    <LinearLayout
                        android:id="@+id/arabicLanguageOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/language_option_bg"
                        android:padding="16dp"
                        android:layout_marginBottom="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_arabic_flag"
                            android:layout_marginEnd="16dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/arabic_language"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/textPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/arabic_language_desc"
                                android:textSize="12sp"
                                android:textColor="@color/textSecondary" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/arabicSelectedIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="@color/islamicGreenDark"
                            android:visibility="visible" />

                    </LinearLayout>

                    <!-- خيار اللغة الإنجليزية -->
                    <LinearLayout
                        android:id="@+id/englishLanguageOption"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:background="@drawable/language_option_bg"
                        android:padding="16dp"
                        android:clickable="true"
                        android:focusable="true">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_english_flag"
                            android:layout_marginEnd="16dp" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/english_language"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:textColor="@color/textPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/english_language_desc"
                                android:textSize="12sp"
                                android:textColor="@color/textSecondary" />

                        </LinearLayout>

                        <ImageView
                            android:id="@+id/englishSelectedIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_check_circle"
                            android:tint="@color/islamicGreenDark"
                            android:visibility="gone" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- قسم الإشعارات -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:background="@color/white">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/notification_settings"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamicGreenDark"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_notifications"
                        android:drawablePadding="12dp"
                        android:gravity="center_vertical" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/enable_notifications"
                                android:textSize="16sp"
                                android:textColor="@color/textPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/notifications_desc"
                                android:textSize="12sp"
                                android:textColor="@color/textSecondary" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/switchNotifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- قسم التشغيل -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:background="@color/white">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/playback_settings"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamicGreenDark"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_play"
                        android:drawablePadding="12dp"
                        android:gravity="center_vertical" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/auto_play"
                                android:textSize="16sp"
                                android:textColor="@color/textPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/auto_play_desc"
                                android:textSize="12sp"
                                android:textColor="@color/textSecondary" />

                        </LinearLayout>

                        <Switch
                            android:id="@+id/switchAutoPlay"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="false" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- قسم حجم الخط -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:background="@color/white">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/text_settings"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamicGreenDark"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_text_size"
                        android:drawablePadding="12dp"
                        android:gravity="center_vertical" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/font_size"
                                android:textSize="16sp"
                                android:textColor="@color/textPrimary" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/font_size_desc"
                                android:textSize="12sp"
                                android:textColor="@color/textSecondary" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/tvFontSize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="16 sp"
                            android:textSize="16sp"
                            android:textColor="@color/islamicGreenDark"
                            android:textStyle="bold"
                            android:background="@drawable/rounded_background"
                            android:padding="8dp" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- إعلان جوجل -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-7841751633097845/8640371910"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
