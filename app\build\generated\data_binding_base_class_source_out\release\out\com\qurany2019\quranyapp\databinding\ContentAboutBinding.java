// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ContentAboutBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final TextView appDescription;

  @NonNull
  public final TextView appTitle;

  @NonNull
  public final TextView appVersionText;

  @NonNull
  public final TextView developerName;

  @NonNull
  public final CardView moreAppsSection;

  @NonNull
  public final CardView privacyPolicySection;

  @NonNull
  public final TextView quranVerse;

  private ContentAboutBinding(@NonNull ScrollView rootView, @NonNull AdView adView,
      @NonNull TextView appDescription, @NonNull TextView appTitle,
      @NonNull TextView appVersionText, @NonNull TextView developerName,
      @NonNull CardView moreAppsSection, @NonNull CardView privacyPolicySection,
      @NonNull TextView quranVerse) {
    this.rootView = rootView;
    this.adView = adView;
    this.appDescription = appDescription;
    this.appTitle = appTitle;
    this.appVersionText = appVersionText;
    this.developerName = developerName;
    this.moreAppsSection = moreAppsSection;
    this.privacyPolicySection = privacyPolicySection;
    this.quranVerse = quranVerse;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ContentAboutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ContentAboutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.content_about, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ContentAboutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.app_description;
      TextView appDescription = ViewBindings.findChildViewById(rootView, id);
      if (appDescription == null) {
        break missingId;
      }

      id = R.id.app_title;
      TextView appTitle = ViewBindings.findChildViewById(rootView, id);
      if (appTitle == null) {
        break missingId;
      }

      id = R.id.app_version_text;
      TextView appVersionText = ViewBindings.findChildViewById(rootView, id);
      if (appVersionText == null) {
        break missingId;
      }

      id = R.id.developer_name;
      TextView developerName = ViewBindings.findChildViewById(rootView, id);
      if (developerName == null) {
        break missingId;
      }

      id = R.id.moreAppsSection;
      CardView moreAppsSection = ViewBindings.findChildViewById(rootView, id);
      if (moreAppsSection == null) {
        break missingId;
      }

      id = R.id.privacyPolicySection;
      CardView privacyPolicySection = ViewBindings.findChildViewById(rootView, id);
      if (privacyPolicySection == null) {
        break missingId;
      }

      id = R.id.quran_verse;
      TextView quranVerse = ViewBindings.findChildViewById(rootView, id);
      if (quranVerse == null) {
        break missingId;
      }

      return new ContentAboutBinding((ScrollView) rootView, adView, appDescription, appTitle,
          appVersionText, developerName, moreAppsSection, privacyPolicySection, quranVerse);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
