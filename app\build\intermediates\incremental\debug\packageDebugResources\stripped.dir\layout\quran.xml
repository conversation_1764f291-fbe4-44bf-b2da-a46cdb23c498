<?xml version='1.0' encoding='utf-8'?>
<RelativeLayout
    xmlns:ns0="http://schemas.android.com/apk/res/android"
    xmlns:ns1="http://schemas.android.com/tools"
    ns0:layout_width="match_parent"
    ns0:layout_height="match_parent"
    ns1:context=".MyActivity"
    ns0:background="#ffffffff"
    ns0:clickable="false"
    ns0:fitsSystemWindows="false">

    <!-- شريط الأزرار (menuBar) في الأعلى -->
    <LinearLayout
        ns0:id="@+id/menuBar"
        ns0:layout_width="match_parent"
        ns0:layout_height="34dp"
        ns0:layout_alignParentTop="true"
        ns0:gravity="bottom"
        ns0:orientation="horizontal"
        ns0:paddingHorizontal="8dp">

        <ImageView
            ns0:id="@+id/suraNext"
            ns0:layout_width="0dp"
            ns0:layout_height="match_parent"
            ns0:layout_weight="1"
            ns0:background="#FFFFFF"
            ns0:scaleType="centerInside"
            ns0:src="@drawable/back" />

        <ImageView
            ns0:id="@+id/fontPlus"
            ns0:layout_width="0dp"
            ns0:layout_height="match_parent"
            ns0:layout_weight="1"
            ns0:background="#FFFFFF"
            ns0:scaleType="centerInside"
            ns0:src="@drawable/plus" />

        <ImageView
            ns0:id="@+id/fontChange"
            ns0:layout_width="0dp"
            ns0:layout_height="match_parent"
            ns0:layout_weight="1"
            ns0:background="#FFFFFF"
            ns0:scaleType="centerInside"
            ns0:src="@drawable/font" />

        <ImageView
            ns0:id="@+id/colorMode"
            ns0:layout_width="0dp"
            ns0:layout_height="match_parent"
            ns0:layout_weight="1"
            ns0:background="#FFFFFF"
            ns0:scaleType="centerInside"
            ns0:src="@drawable/color" />

        <ImageView
            ns0:id="@+id/fontMinus"
            ns0:layout_width="0dp"
            ns0:layout_height="match_parent"
            ns0:layout_weight="1"
            ns0:background="#FFFFFF"
            ns0:scaleType="centerInside"
            ns0:src="@drawable/minus" />

        <ImageView
            ns0:id="@+id/suraPrev"
            ns0:layout_width="0dp"
            ns0:layout_height="match_parent"
            ns0:layout_weight="1"
            ns0:background="#FFFFFF"
            ns0:scaleType="centerInside"
            ns0:src="@drawable/next" />
    </LinearLayout>

    <!-- الشريط البني (فاصل) تحت الأزرار -->
    <FrameLayout
        ns0:id="@+id/menuContainer"
        ns0:layout_width="match_parent"
        ns0:layout_height="5dp"
        ns0:layout_below="@id/menuBar"
        ns0:background="#795548">

        <ImageView
            ns0:layout_width="match_parent"
            ns0:layout_height="match_parent"
            ns0:scaleType="centerCrop"
            ns0:src="@drawable/banne"
            ns0:forceDarkAllowed="false" />
    </FrameLayout>

    <!-- المحتوى الرئيسي -->
    <LinearLayout
        ns0:id="@+id/Snd"
        ns0:layout_width="match_parent"
        ns0:layout_height="match_parent"
        ns0:orientation="vertical"
        ns0:layout_below="@id/menuContainer"
        ns0:padding="16dp"
        ns0:gravity="center_horizontal">

        <Spinner
            ns0:id="@+id/suraSelect"
            ns0:layout_width="match_parent"
            ns0:layout_height="22dp"
            ns0:layout_centerInParent="true"
            ns0:background="@drawable/ban"
            ns0:spinnerMode="dropdown"
            ns0:textAlignment="gravity" />

        <FrameLayout
            ns0:layout_width="match_parent"
            ns0:layout_height="wrap_content"
            ns0:layout_marginTop="4dp"
            ns0:layout_marginBottom="8dp">

            <ImageView
                ns0:layout_width="match_parent"
                ns0:layout_height="47dp"
                ns0:scaleType="fitXY"
                ns0:src="@drawable/islamic"
                ns0:forceDarkAllowed="false" />

            <TextView
                ns0:id="@+id/suraTitle"
                ns0:layout_width="match_parent"
                ns0:layout_height="match_parent"
                ns0:layout_gravity="center"
                ns0:gravity="center"
                ns0:padding="4dp"
                ns0:text="سورة الفاتحة"
                ns0:textColor="#000000"
                ns0:textSize="30sp"
                ns0:textStyle="bold" />
        </FrameLayout>

        <ScrollView
            ns0:layout_width="match_parent"
            ns0:layout_height="0dp"
            ns0:layout_weight="1"
            ns0:id="@+id/scrollView"
            ns0:overScrollMode="never"
            ns0:fadingEdge="none"
            ns0:nestedScrollingEnabled="false">

            <TextView
                ns0:layout_width="match_parent"
                ns0:layout_height="wrap_content"
                ns0:id="@+id/quranMain"
                ns0:textIsSelectable="true"
                ns0:background="#ffffffff"
                ns0:autoText="false"
                ns0:padding="15dp"
                ns0:textAlignment="center"
                ns0:textColor="#fffdfaf7"
                ns0:textSize="16dp"
                ns0:textStyle="normal"
                ns0:fadeScrollbars="true"
                ns0:overScrollMode="ifContentScrolls"
                ns0:fadingEdge="vertical|horizontal"
                ns0:fadingEdgeLength="20dp"
                ns0:lineSpacingExtra="5dp"
                ns0:scrollbars="vertical"
                ns0:gravity="center" />
        </ScrollView>
    </LinearLayout>
</RelativeLayout>