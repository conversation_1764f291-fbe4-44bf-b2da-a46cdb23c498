<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:context="com.qurany2019.quranyapp.PrayerTimesActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Theme.QuranyApp.AppBarOverlay"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/Theme.QuranyApp.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/prayer_times_background"
        app:layout_constraintTop_toBottomOf="@id/app_bar"
        app:layout_constraintBottom_toTopOf="@id/adView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header مع التاريخ والوقت -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="20dp"
                app:cardElevation="12dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="28dp"
                    android:background="@drawable/prayer_header_gradient"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/prayer_times_title"
                        android:textSize="26sp"
                        android:textStyle="bold"
                        android:textColor="@color/prayer_header_text"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvCityName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/riyadh"
                        android:textSize="14sp"
                        android:textColor="@color/prayer_name_text"
                        android:background="@drawable/prayer_badge_gold"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="6dp"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tvCurrentDate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/current_date"
                        android:textSize="16sp"
                        android:textColor="@color/prayer_header_text"
                        android:alpha="0.9"
                        android:layout_marginBottom="6dp" />

                    <TextView
                        android:id="@+id/tvCurrentTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="14:30"
                        android:textSize="36sp"
                        android:textStyle="bold"
                        android:textColor="@color/prayer_header_text"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tvNextPrayer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الصلاة القادمة: العصر - 15:30"
                        android:textSize="14sp"
                        android:textColor="@color/prayer_name_text"
                        android:background="@drawable/prayer_badge_next"
                        android:paddingHorizontal="12dp"
                        android:paddingVertical="8dp" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- أوقات الصلاة -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/prayer_schedule"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/islamicGreenDark"
                android:layout_marginBottom="16dp"
                android:gravity="center" />

            <!-- الفجر -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardFajr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:background="@color/fajr_card_bg"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_fajr"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/circular_icon_background_small"
                        android:backgroundTint="@color/fajr_icon_bg"
                        android:padding="10dp"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/fajr"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/prayer_name_text" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/fajr_prayer"
                            android:textSize="12sp"
                            android:textColor="@color/prayer_description_text" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvFajr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="05:30"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/fajr_time_color" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- الشروق -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardSunrise"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:background="@color/sunrise_card_bg"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_sunrise"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/circular_icon_background_small"
                        android:backgroundTint="@color/sunrise_icon_bg"
                        android:padding="10dp"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sunrise"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/prayer_name_text" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/sunrise_time"
                            android:textSize="12sp"
                            android:textColor="@color/prayer_description_text" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvSunrise"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="06:45"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/sunrise_time_color" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- الظهر -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardDhuhr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:background="@color/dhuhr_card_bg"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_dhuhr"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/circular_icon_background_small"
                        android:backgroundTint="@color/dhuhr_icon_bg"
                        android:padding="10dp"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/dhuhr"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/prayer_name_text" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/dhuhr_prayer"
                            android:textSize="12sp"
                            android:textColor="@color/prayer_description_text" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvDhuhr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="12:15"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/dhuhr_time_color" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- العصر -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardAsr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:background="@color/asr_card_bg"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_asr"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/circular_icon_background_small"
                        android:backgroundTint="@color/asr_icon_bg"
                        android:padding="10dp"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/asr"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/prayer_name_text" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/asr_prayer"
                            android:textSize="12sp"
                            android:textColor="@color/prayer_description_text" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvAsr"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="15:30"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/asr_time_color" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- المغرب -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardMaghrib"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:background="@color/maghrib_card_bg"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_maghrib"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/circular_icon_background_small"
                        android:backgroundTint="@color/maghrib_icon_bg"
                        android:padding="10dp"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/maghrib"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/prayer_name_text" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/maghrib_prayer"
                            android:textSize="12sp"
                            android:textColor="@color/prayer_description_text" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvMaghrib"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="18:20"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/maghrib_time_color" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- العشاء -->
            <androidx.cardview.widget.CardView
                android:id="@+id/cardIsha"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="20dp"
                    android:background="@color/isha_card_bg"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_isha"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/circular_icon_background_small"
                        android:backgroundTint="@color/isha_icon_bg"
                        android:padding="10dp"
                        android:tint="@color/white" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/isha"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/prayer_name_text" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/isha_prayer"
                            android:textSize="12sp"
                            android:textColor="@color/prayer_description_text" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvIsha"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="19:45"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/isha_time_color" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- إعدادات الإشعارات -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:background="@color/white">

                    <!-- عنوان الإشعارات -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_mosque"
                            android:tint="@color/islamicGreenDark" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="12dp"
                            android:text="@string/prayer_notifications"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/islamicGreenDark" />

                        <Switch
                            android:id="@+id/switchNotifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- معلومات الإشعارات -->
                    <TextView
                        android:id="@+id/tvNotificationInfo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/notification_desc"
                        android:textSize="12sp"
                        android:textColor="@color/textSecondary"
                        android:lineSpacingExtra="2dp" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>



        </LinearLayout>

    </ScrollView>

    <!-- إعلان جوجل -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-7841751633097845/8640371910"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
