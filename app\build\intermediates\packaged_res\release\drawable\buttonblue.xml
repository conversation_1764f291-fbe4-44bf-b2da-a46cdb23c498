<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

        <item android:state_enabled="false">
            <shape>
                <gradient android:startColor="#4E342E" android:endColor="#4E342E" android:angle="270" />
                <stroke android:width="3px" android:color="#4E342E" />
                <corners android:radius="7px" />
                <size android:width="250px" android:height="50px" />
            </shape>
        </item>

        <item android:state_pressed="true">
            <shape>
                <gradient android:startColor="#4E342E" android:endColor="#4E342E" android:angle="270" />
                <stroke android:width="3px" android:color="#4E342E" />
                <corners android:radius="7px" />
            </shape>
        </item>

        <item android:state_focused="true">
            <shape>
                <gradient android:startColor="#4E342E" android:endColor="#4E342E" android:angle="270" />
                <stroke android:width="3px" android:color="#4E342E" />
                <corners android:radius="7px" />
            </shape>
        </item>

        <item>
            <shape>
                <gradient android:startColor="#4E342E" android:endColor="#4E342E" android:angle="270" />
                <stroke android:width="3px" android:color="#4E342E" />
                <corners android:radius="7px" />
                <size android:width="250px" android:height="50px" />
            </shape>
        </item>
</selector>