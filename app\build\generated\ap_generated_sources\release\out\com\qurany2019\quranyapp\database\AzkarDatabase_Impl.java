package com.qurany2019.quranyapp.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomOpenHelper;
import androidx.room.RoomOpenHelper.Delegate;
import androidx.room.RoomOpenHelper.ValidationResult;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.room.util.TableInfo.Column;
import androidx.room.util.TableInfo.ForeignKey;
import androidx.room.util.TableInfo.Index;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import androidx.sqlite.db.SupportSQLiteOpenHelper.Callback;
import androidx.sqlite.db.SupportSQLiteOpenHelper.Configuration;
import com.qurany2019.quranyapp.database.dao.AchievementDao;
import com.qurany2019.quranyapp.database.dao.AchievementDao_Impl;
import com.qurany2019.quranyapp.database.dao.AzkarDao;
import com.qurany2019.quranyapp.database.dao.AzkarDao_Impl;
import com.qurany2019.quranyapp.database.dao.AzkarProgressDao;
import com.qurany2019.quranyapp.database.dao.AzkarProgressDao_Impl;
import com.qurany2019.quranyapp.database.dao.AzkarStatisticsDao;
import com.qurany2019.quranyapp.database.dao.AzkarStatisticsDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AzkarDatabase_Impl extends AzkarDatabase {
  private volatile AzkarDao _azkarDao;

  private volatile AzkarProgressDao _azkarProgressDao;

  private volatile AzkarStatisticsDao _azkarStatisticsDao;

  private volatile AchievementDao _achievementDao;

  @Override
  protected SupportSQLiteOpenHelper createOpenHelper(DatabaseConfiguration configuration) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(configuration, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(SupportSQLiteDatabase _db) {
        _db.execSQL("CREATE TABLE IF NOT EXISTS `azkar_table` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `azkar_id` TEXT, `title` TEXT, `content` TEXT, `target_count` INTEGER NOT NULL, `category` TEXT, `order_index` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)");
        _db.execSQL("CREATE TABLE IF NOT EXISTS `azkar_progress_table` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `azkar_id` INTEGER NOT NULL, `current_count` INTEGER NOT NULL, `target_count` INTEGER NOT NULL, `is_completed` INTEGER NOT NULL, `date` TEXT, `start_time` INTEGER NOT NULL, `completion_time` INTEGER NOT NULL, `session_duration` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL, FOREIGN KEY(`azkar_id`) REFERENCES `azkar_table`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE )");
        _db.execSQL("CREATE INDEX IF NOT EXISTS `index_azkar_progress_table_azkar_id` ON `azkar_progress_table` (`azkar_id`)");
        _db.execSQL("CREATE INDEX IF NOT EXISTS `index_azkar_progress_table_date` ON `azkar_progress_table` (`date`)");
        _db.execSQL("CREATE TABLE IF NOT EXISTS `azkar_statistics_table` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `date` TEXT, `category` TEXT, `total_azkar_count` INTEGER NOT NULL, `completed_azkar_count` INTEGER NOT NULL, `total_tasbih_count` INTEGER NOT NULL, `completion_percentage` REAL NOT NULL, `session_duration` INTEGER NOT NULL, `streak_days` INTEGER NOT NULL, `best_completion_time` INTEGER NOT NULL, `average_completion_time` INTEGER NOT NULL, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)");
        _db.execSQL("CREATE INDEX IF NOT EXISTS `index_azkar_statistics_table_date` ON `azkar_statistics_table` (`date`)");
        _db.execSQL("CREATE INDEX IF NOT EXISTS `index_azkar_statistics_table_category` ON `azkar_statistics_table` (`category`)");
        _db.execSQL("CREATE TABLE IF NOT EXISTS `achievements_table` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `achievement_id` TEXT, `title` TEXT, `description` TEXT, `achievement_type` TEXT, `target_value` INTEGER NOT NULL, `current_value` INTEGER NOT NULL, `is_unlocked` INTEGER NOT NULL, `unlock_date` INTEGER NOT NULL, `icon_resource` TEXT, `reward_points` INTEGER NOT NULL, `category` TEXT, `created_at` INTEGER NOT NULL, `updated_at` INTEGER NOT NULL)");
        _db.execSQL("CREATE INDEX IF NOT EXISTS `index_achievements_table_achievement_type` ON `achievements_table` (`achievement_type`)");
        _db.execSQL("CREATE INDEX IF NOT EXISTS `index_achievements_table_is_unlocked` ON `achievements_table` (`is_unlocked`)");
        _db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        _db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '107c8474d020d0649f2da8a2cb12f235')");
      }

      @Override
      public void dropAllTables(SupportSQLiteDatabase _db) {
        _db.execSQL("DROP TABLE IF EXISTS `azkar_table`");
        _db.execSQL("DROP TABLE IF EXISTS `azkar_progress_table`");
        _db.execSQL("DROP TABLE IF EXISTS `azkar_statistics_table`");
        _db.execSQL("DROP TABLE IF EXISTS `achievements_table`");
        if (mCallbacks != null) {
          for (int _i = 0, _size = mCallbacks.size(); _i < _size; _i++) {
            mCallbacks.get(_i).onDestructiveMigration(_db);
          }
        }
      }

      @Override
      public void onCreate(SupportSQLiteDatabase _db) {
        if (mCallbacks != null) {
          for (int _i = 0, _size = mCallbacks.size(); _i < _size; _i++) {
            mCallbacks.get(_i).onCreate(_db);
          }
        }
      }

      @Override
      public void onOpen(SupportSQLiteDatabase _db) {
        mDatabase = _db;
        _db.execSQL("PRAGMA foreign_keys = ON");
        internalInitInvalidationTracker(_db);
        if (mCallbacks != null) {
          for (int _i = 0, _size = mCallbacks.size(); _i < _size; _i++) {
            mCallbacks.get(_i).onOpen(_db);
          }
        }
      }

      @Override
      public void onPreMigrate(SupportSQLiteDatabase _db) {
        DBUtil.dropFtsSyncTriggers(_db);
      }

      @Override
      public void onPostMigrate(SupportSQLiteDatabase _db) {
      }

      @Override
      public RoomOpenHelper.ValidationResult onValidateSchema(SupportSQLiteDatabase _db) {
        final HashMap<String, TableInfo.Column> _columnsAzkarTable = new HashMap<String, TableInfo.Column>(9);
        _columnsAzkarTable.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("azkar_id", new TableInfo.Column("azkar_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("title", new TableInfo.Column("title", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("content", new TableInfo.Column("content", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("target_count", new TableInfo.Column("target_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("category", new TableInfo.Column("category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("order_index", new TableInfo.Column("order_index", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarTable.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAzkarTable = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAzkarTable = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAzkarTable = new TableInfo("azkar_table", _columnsAzkarTable, _foreignKeysAzkarTable, _indicesAzkarTable);
        final TableInfo _existingAzkarTable = TableInfo.read(_db, "azkar_table");
        if (! _infoAzkarTable.equals(_existingAzkarTable)) {
          return new RoomOpenHelper.ValidationResult(false, "azkar_table(com.qurany2019.quranyapp.database.entities.AzkarEntity).\n"
                  + " Expected:\n" + _infoAzkarTable + "\n"
                  + " Found:\n" + _existingAzkarTable);
        }
        final HashMap<String, TableInfo.Column> _columnsAzkarProgressTable = new HashMap<String, TableInfo.Column>(11);
        _columnsAzkarProgressTable.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("azkar_id", new TableInfo.Column("azkar_id", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("current_count", new TableInfo.Column("current_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("target_count", new TableInfo.Column("target_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("is_completed", new TableInfo.Column("is_completed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("date", new TableInfo.Column("date", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("start_time", new TableInfo.Column("start_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("completion_time", new TableInfo.Column("completion_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("session_duration", new TableInfo.Column("session_duration", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarProgressTable.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAzkarProgressTable = new HashSet<TableInfo.ForeignKey>(1);
        _foreignKeysAzkarProgressTable.add(new TableInfo.ForeignKey("azkar_table", "CASCADE", "NO ACTION",Arrays.asList("azkar_id"), Arrays.asList("id")));
        final HashSet<TableInfo.Index> _indicesAzkarProgressTable = new HashSet<TableInfo.Index>(2);
        _indicesAzkarProgressTable.add(new TableInfo.Index("index_azkar_progress_table_azkar_id", false, Arrays.asList("azkar_id"), Arrays.asList("ASC")));
        _indicesAzkarProgressTable.add(new TableInfo.Index("index_azkar_progress_table_date", false, Arrays.asList("date"), Arrays.asList("ASC")));
        final TableInfo _infoAzkarProgressTable = new TableInfo("azkar_progress_table", _columnsAzkarProgressTable, _foreignKeysAzkarProgressTable, _indicesAzkarProgressTable);
        final TableInfo _existingAzkarProgressTable = TableInfo.read(_db, "azkar_progress_table");
        if (! _infoAzkarProgressTable.equals(_existingAzkarProgressTable)) {
          return new RoomOpenHelper.ValidationResult(false, "azkar_progress_table(com.qurany2019.quranyapp.database.entities.AzkarProgressEntity).\n"
                  + " Expected:\n" + _infoAzkarProgressTable + "\n"
                  + " Found:\n" + _existingAzkarProgressTable);
        }
        final HashMap<String, TableInfo.Column> _columnsAzkarStatisticsTable = new HashMap<String, TableInfo.Column>(13);
        _columnsAzkarStatisticsTable.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("date", new TableInfo.Column("date", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("category", new TableInfo.Column("category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("total_azkar_count", new TableInfo.Column("total_azkar_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("completed_azkar_count", new TableInfo.Column("completed_azkar_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("total_tasbih_count", new TableInfo.Column("total_tasbih_count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("completion_percentage", new TableInfo.Column("completion_percentage", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("session_duration", new TableInfo.Column("session_duration", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("streak_days", new TableInfo.Column("streak_days", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("best_completion_time", new TableInfo.Column("best_completion_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("average_completion_time", new TableInfo.Column("average_completion_time", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAzkarStatisticsTable.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAzkarStatisticsTable = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAzkarStatisticsTable = new HashSet<TableInfo.Index>(2);
        _indicesAzkarStatisticsTable.add(new TableInfo.Index("index_azkar_statistics_table_date", false, Arrays.asList("date"), Arrays.asList("ASC")));
        _indicesAzkarStatisticsTable.add(new TableInfo.Index("index_azkar_statistics_table_category", false, Arrays.asList("category"), Arrays.asList("ASC")));
        final TableInfo _infoAzkarStatisticsTable = new TableInfo("azkar_statistics_table", _columnsAzkarStatisticsTable, _foreignKeysAzkarStatisticsTable, _indicesAzkarStatisticsTable);
        final TableInfo _existingAzkarStatisticsTable = TableInfo.read(_db, "azkar_statistics_table");
        if (! _infoAzkarStatisticsTable.equals(_existingAzkarStatisticsTable)) {
          return new RoomOpenHelper.ValidationResult(false, "azkar_statistics_table(com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity).\n"
                  + " Expected:\n" + _infoAzkarStatisticsTable + "\n"
                  + " Found:\n" + _existingAzkarStatisticsTable);
        }
        final HashMap<String, TableInfo.Column> _columnsAchievementsTable = new HashMap<String, TableInfo.Column>(14);
        _columnsAchievementsTable.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("achievement_id", new TableInfo.Column("achievement_id", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("title", new TableInfo.Column("title", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("description", new TableInfo.Column("description", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("achievement_type", new TableInfo.Column("achievement_type", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("target_value", new TableInfo.Column("target_value", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("current_value", new TableInfo.Column("current_value", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("is_unlocked", new TableInfo.Column("is_unlocked", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("unlock_date", new TableInfo.Column("unlock_date", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("icon_resource", new TableInfo.Column("icon_resource", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("reward_points", new TableInfo.Column("reward_points", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("category", new TableInfo.Column("category", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("created_at", new TableInfo.Column("created_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAchievementsTable.put("updated_at", new TableInfo.Column("updated_at", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAchievementsTable = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAchievementsTable = new HashSet<TableInfo.Index>(2);
        _indicesAchievementsTable.add(new TableInfo.Index("index_achievements_table_achievement_type", false, Arrays.asList("achievement_type"), Arrays.asList("ASC")));
        _indicesAchievementsTable.add(new TableInfo.Index("index_achievements_table_is_unlocked", false, Arrays.asList("is_unlocked"), Arrays.asList("ASC")));
        final TableInfo _infoAchievementsTable = new TableInfo("achievements_table", _columnsAchievementsTable, _foreignKeysAchievementsTable, _indicesAchievementsTable);
        final TableInfo _existingAchievementsTable = TableInfo.read(_db, "achievements_table");
        if (! _infoAchievementsTable.equals(_existingAchievementsTable)) {
          return new RoomOpenHelper.ValidationResult(false, "achievements_table(com.qurany2019.quranyapp.database.entities.AchievementEntity).\n"
                  + " Expected:\n" + _infoAchievementsTable + "\n"
                  + " Found:\n" + _existingAchievementsTable);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "107c8474d020d0649f2da8a2cb12f235", "60e93ac38e76d3b7d70a3cef26fc90f0");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(configuration.context)
        .name(configuration.name)
        .callback(_openCallback)
        .build();
    final SupportSQLiteOpenHelper _helper = configuration.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "azkar_table","azkar_progress_table","azkar_statistics_table","achievements_table");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    boolean _supportsDeferForeignKeys = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP;
    try {
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = FALSE");
      }
      super.beginTransaction();
      if (_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA defer_foreign_keys = TRUE");
      }
      _db.execSQL("DELETE FROM `azkar_table`");
      _db.execSQL("DELETE FROM `azkar_progress_table`");
      _db.execSQL("DELETE FROM `azkar_statistics_table`");
      _db.execSQL("DELETE FROM `achievements_table`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      if (!_supportsDeferForeignKeys) {
        _db.execSQL("PRAGMA foreign_keys = TRUE");
      }
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(AzkarDao.class, AzkarDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AzkarProgressDao.class, AzkarProgressDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AzkarStatisticsDao.class, AzkarStatisticsDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AchievementDao.class, AchievementDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  public List<Migration> getAutoMigrations(
      @NonNull Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecsMap) {
    return Arrays.asList();
  }

  @Override
  public AzkarDao azkarDao() {
    if (_azkarDao != null) {
      return _azkarDao;
    } else {
      synchronized(this) {
        if(_azkarDao == null) {
          _azkarDao = new AzkarDao_Impl(this);
        }
        return _azkarDao;
      }
    }
  }

  @Override
  public AzkarProgressDao azkarProgressDao() {
    if (_azkarProgressDao != null) {
      return _azkarProgressDao;
    } else {
      synchronized(this) {
        if(_azkarProgressDao == null) {
          _azkarProgressDao = new AzkarProgressDao_Impl(this);
        }
        return _azkarProgressDao;
      }
    }
  }

  @Override
  public AzkarStatisticsDao azkarStatisticsDao() {
    if (_azkarStatisticsDao != null) {
      return _azkarStatisticsDao;
    } else {
      synchronized(this) {
        if(_azkarStatisticsDao == null) {
          _azkarStatisticsDao = new AzkarStatisticsDao_Impl(this);
        }
        return _azkarStatisticsDao;
      }
    }
  }

  @Override
  public AchievementDao achievementDao() {
    if (_achievementDao != null) {
      return _achievementDao;
    } else {
      synchronized(this) {
        if(_achievementDao == null) {
          _achievementDao = new AchievementDao_Impl(this);
        }
        return _achievementDao;
      }
    }
  }
}
