<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context="com.qurany2019.quranyapp.Splash">

    <ImageView
        android:id="@+id/logoImage"
        android:layout_width="669dp"
        android:layout_height="316dp"
        android:layout_marginTop="164dp"
        android:src="@drawable/logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/quranVerseImage"
        android:layout_width="343dp"
        android:layout_height="143dp"
        android:layout_marginTop="8dp"
        android:contentDescription="@string/quran_verse"
        android:src="@drawable/qran"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logoImage" />

</androidx.constraintlayout.widget.ConstraintLayout>
