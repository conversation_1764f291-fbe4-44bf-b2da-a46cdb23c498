<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.IslamicApp" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/islamicGreenMedium</item>
        <item name="colorPrimaryVariant">@color/islamicGreenDark</item>
        <item name="colorOnPrimary">@color/textOnPrimary</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/islamicGold</item>
        <item name="colorSecondaryVariant">@color/islamicGoldLight</item>
        <item name="colorOnSecondary">@color/textPrimary</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/backgroundPrimary</item>
        <item name="android:textColorPrimary">@color/textPrimary</item>
        <item name="android:textColorSecondary">@color/textSecondary</item>
    </style>

    <!-- ثيم عام للتطبيق -->
    <style name="Theme.QuranyApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/islamicGreenMedium</item>
        <item name="colorPrimaryVariant">@color/islamicGreenDark</item>
        <item name="colorOnPrimary">@color/textOnPrimary</item>
        <item name="colorSecondary">@color/islamicGold</item>
        <item name="colorOnSecondary">@color/textPrimary</item>
        <item name="colorAccent">@color/islamicGold</item>
        <item name="colorSurface">@color/backgroundSecondary</item>
        <item name="colorOnSurface">@color/textPrimary</item>
        <item name="colorSurfaceVariant">@color/backgroundPrimary</item>
        <item name="colorOutline">@color/dividerColor</item>

        <item name="android:statusBarColor">@color/islamicGreenDark</item>
        <!-- إعداد شريط الأزرار للون الافتراضي الأسود -->
        <item name="android:navigationBarColor">@android:color/black</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:fontFamily">@font/noto</item>

        <!-- إعدادات Edge-to-Edge -->
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:enforceStatusBarContrast">false</item>
        <item name="android:enforceNavigationBarContrast">false</item>
    </style>

    <style name="Theme.IslamicApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.IslamicApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.IslamicApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- Card Styles -->
    <style name="IslamicCardStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
    </style>

    <!-- Button Styles -->
    <style name="IslamicButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="backgroundTint">@color/islamicGreenMedium</item>
        <item name="cornerRadius">16dp</item>
    </style>

    <style name="IslamicButtonOutlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="strokeColor">@color/islamicGreenMedium</item>
        <item name="android:textColor">@color/islamicGreenMedium</item>
        <item name="cornerRadius">16dp</item>
    </style>

    <!-- Toolbar Styles -->
    <style name="IslamicToolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/islamicGreenMedium</item>
        <item name="titleTextColor">@color/textOnPrimary</item>
        <item name="subtitleTextColor">@color/textOnPrimary</item>
    </style>

</resources>
