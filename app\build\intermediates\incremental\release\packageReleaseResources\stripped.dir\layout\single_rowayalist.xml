<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="6dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    android:foreground="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:background="@drawable/beautiful_surah_background">

        <!-- صورة غلاف القرآن مع رقم السورة -->
        <RelativeLayout
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:layout_marginEnd="12dp">

            <!-- صورة غلاف القرآن -->
            <ImageView
                android:id="@+id/quranCoverImage"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/sur"
                android:scaleType="centerCrop"
                android:background="@drawable/quran_image_frame" />

            <!-- رقم السورة في الزاوية -->
            <TextView
                android:id="@+id/surahNumber"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true"
                android:layout_marginTop="-2dp"
                android:layout_marginEnd="-2dp"
                android:text="1"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:gravity="center"
                android:background="@drawable/number_badge" />

        </RelativeLayout>

        <!-- معلومات السورة -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="4dp">

            <!-- اسم السورة -->
            <TextView
                android:id="@+id/textView1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الفاتحة"
                android:textColor="@color/islamicTextPrimary"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="serif"
                android:layout_marginBottom="2dp" />

            <!-- معلومات إضافية (سيتم إخفاؤها مؤقتاً) -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:visibility="gone">

                <!-- نوع السورة -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="مكية"
                    android:textColor="@color/islamicGold"
                    android:textSize="12sp"
                    android:background="@drawable/surah_type_badge"
                    android:padding="4dp"
                    android:layout_marginEnd="8dp" />

                <!-- عدد الآيات -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="7 آيات"
                    android:textColor="@color/textSecondary"
                    android:textSize="12sp"
                    android:layout_marginEnd="8dp" />

            </LinearLayout>

            <!-- حالة السورة -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="4dp">

                <ImageView
                    android:id="@+id/statusIcon"
                    android:layout_width="14dp"
                    android:layout_height="14dp"
                    android:src="@drawable/ic_check_circle"
                    android:layout_marginEnd="4dp"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="جاهزة للتشغيل"
                    android:textColor="@color/islamicGreenMedium"
                    android:textSize="12sp"
                    android:fontFamily="serif" />

            </LinearLayout>

        </LinearLayout>

        <!-- الأزرار الواضحة والمعبرة -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- زر التنزيل -->
            <RelativeLayout
                android:id="@+id/button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/download_button_background">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/ic_download_arrow"
                    android:tint="@android:color/white" />

            </RelativeLayout>

            <!-- زر التشغيل -->
            <RelativeLayout
                android:id="@+id/imageView"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/play_button_background">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/ic_play_arrow"
                    android:tint="@android:color/white" />

            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
