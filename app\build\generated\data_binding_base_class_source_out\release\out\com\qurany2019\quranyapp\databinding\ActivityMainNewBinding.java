// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.navigation.NavigationView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainNewBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final ImageView azkarIcon;

  @NonNull
  public final LinearLayout azkarNavItem;

  @NonNull
  public final LinearLayout bottomNavigation;

  @NonNull
  public final RelativeLayout bottomNavigationContainer;

  @NonNull
  public final CardView btnAzkar;

  @NonNull
  public final CardView btnDua;

  @NonNull
  public final CardView btnPrayerTimes;

  @NonNull
  public final CardView btnQibla;

  @NonNull
  public final CardView btnQuranSuras;

  @NonNull
  public final TextView currentTime;

  @NonNull
  public final ImageView darkModeIcon;

  @NonNull
  public final TextView dayText;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final ImageView homeIcon;

  @NonNull
  public final LinearLayout homeNavItem;

  @NonNull
  public final TextView lastReadSurah;

  @NonNull
  public final ImageView listenIcon;

  @NonNull
  public final LinearLayout listenNavItem;

  @NonNull
  public final ImageView menuIcon;

  @NonNull
  public final ImageView moreIcon;

  @NonNull
  public final LinearLayout moreNavItem;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final ImageView notificationIcon;

  @NonNull
  public final ImageView quranIcon;

  @NonNull
  public final LinearLayout quranNavItem;

  @NonNull
  public final ImageView quranSectionIcon;

  private ActivityMainNewBinding(@NonNull DrawerLayout rootView, @NonNull ImageView azkarIcon,
      @NonNull LinearLayout azkarNavItem, @NonNull LinearLayout bottomNavigation,
      @NonNull RelativeLayout bottomNavigationContainer, @NonNull CardView btnAzkar,
      @NonNull CardView btnDua, @NonNull CardView btnPrayerTimes, @NonNull CardView btnQibla,
      @NonNull CardView btnQuranSuras, @NonNull TextView currentTime,
      @NonNull ImageView darkModeIcon, @NonNull TextView dayText,
      @NonNull DrawerLayout drawerLayout, @NonNull ImageView homeIcon,
      @NonNull LinearLayout homeNavItem, @NonNull TextView lastReadSurah,
      @NonNull ImageView listenIcon, @NonNull LinearLayout listenNavItem,
      @NonNull ImageView menuIcon, @NonNull ImageView moreIcon, @NonNull LinearLayout moreNavItem,
      @NonNull NavigationView navView, @NonNull ImageView notificationIcon,
      @NonNull ImageView quranIcon, @NonNull LinearLayout quranNavItem,
      @NonNull ImageView quranSectionIcon) {
    this.rootView = rootView;
    this.azkarIcon = azkarIcon;
    this.azkarNavItem = azkarNavItem;
    this.bottomNavigation = bottomNavigation;
    this.bottomNavigationContainer = bottomNavigationContainer;
    this.btnAzkar = btnAzkar;
    this.btnDua = btnDua;
    this.btnPrayerTimes = btnPrayerTimes;
    this.btnQibla = btnQibla;
    this.btnQuranSuras = btnQuranSuras;
    this.currentTime = currentTime;
    this.darkModeIcon = darkModeIcon;
    this.dayText = dayText;
    this.drawerLayout = drawerLayout;
    this.homeIcon = homeIcon;
    this.homeNavItem = homeNavItem;
    this.lastReadSurah = lastReadSurah;
    this.listenIcon = listenIcon;
    this.listenNavItem = listenNavItem;
    this.menuIcon = menuIcon;
    this.moreIcon = moreIcon;
    this.moreNavItem = moreNavItem;
    this.navView = navView;
    this.notificationIcon = notificationIcon;
    this.quranIcon = quranIcon;
    this.quranNavItem = quranNavItem;
    this.quranSectionIcon = quranSectionIcon;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainNewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainNewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main_new, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainNewBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.azkarIcon;
      ImageView azkarIcon = ViewBindings.findChildViewById(rootView, id);
      if (azkarIcon == null) {
        break missingId;
      }

      id = R.id.azkarNavItem;
      LinearLayout azkarNavItem = ViewBindings.findChildViewById(rootView, id);
      if (azkarNavItem == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      LinearLayout bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.bottomNavigationContainer;
      RelativeLayout bottomNavigationContainer = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigationContainer == null) {
        break missingId;
      }

      id = R.id.btnAzkar;
      CardView btnAzkar = ViewBindings.findChildViewById(rootView, id);
      if (btnAzkar == null) {
        break missingId;
      }

      id = R.id.btnDua;
      CardView btnDua = ViewBindings.findChildViewById(rootView, id);
      if (btnDua == null) {
        break missingId;
      }

      id = R.id.btnPrayerTimes;
      CardView btnPrayerTimes = ViewBindings.findChildViewById(rootView, id);
      if (btnPrayerTimes == null) {
        break missingId;
      }

      id = R.id.btnQibla;
      CardView btnQibla = ViewBindings.findChildViewById(rootView, id);
      if (btnQibla == null) {
        break missingId;
      }

      id = R.id.btnQuranSuras;
      CardView btnQuranSuras = ViewBindings.findChildViewById(rootView, id);
      if (btnQuranSuras == null) {
        break missingId;
      }

      id = R.id.currentTime;
      TextView currentTime = ViewBindings.findChildViewById(rootView, id);
      if (currentTime == null) {
        break missingId;
      }

      id = R.id.darkModeIcon;
      ImageView darkModeIcon = ViewBindings.findChildViewById(rootView, id);
      if (darkModeIcon == null) {
        break missingId;
      }

      id = R.id.dayText;
      TextView dayText = ViewBindings.findChildViewById(rootView, id);
      if (dayText == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.homeIcon;
      ImageView homeIcon = ViewBindings.findChildViewById(rootView, id);
      if (homeIcon == null) {
        break missingId;
      }

      id = R.id.homeNavItem;
      LinearLayout homeNavItem = ViewBindings.findChildViewById(rootView, id);
      if (homeNavItem == null) {
        break missingId;
      }

      id = R.id.lastReadSurah;
      TextView lastReadSurah = ViewBindings.findChildViewById(rootView, id);
      if (lastReadSurah == null) {
        break missingId;
      }

      id = R.id.listenIcon;
      ImageView listenIcon = ViewBindings.findChildViewById(rootView, id);
      if (listenIcon == null) {
        break missingId;
      }

      id = R.id.listenNavItem;
      LinearLayout listenNavItem = ViewBindings.findChildViewById(rootView, id);
      if (listenNavItem == null) {
        break missingId;
      }

      id = R.id.menuIcon;
      ImageView menuIcon = ViewBindings.findChildViewById(rootView, id);
      if (menuIcon == null) {
        break missingId;
      }

      id = R.id.moreIcon;
      ImageView moreIcon = ViewBindings.findChildViewById(rootView, id);
      if (moreIcon == null) {
        break missingId;
      }

      id = R.id.moreNavItem;
      LinearLayout moreNavItem = ViewBindings.findChildViewById(rootView, id);
      if (moreNavItem == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.notificationIcon;
      ImageView notificationIcon = ViewBindings.findChildViewById(rootView, id);
      if (notificationIcon == null) {
        break missingId;
      }

      id = R.id.quranIcon;
      ImageView quranIcon = ViewBindings.findChildViewById(rootView, id);
      if (quranIcon == null) {
        break missingId;
      }

      id = R.id.quranNavItem;
      LinearLayout quranNavItem = ViewBindings.findChildViewById(rootView, id);
      if (quranNavItem == null) {
        break missingId;
      }

      id = R.id.quranSectionIcon;
      ImageView quranSectionIcon = ViewBindings.findChildViewById(rootView, id);
      if (quranSectionIcon == null) {
        break missingId;
      }

      return new ActivityMainNewBinding((DrawerLayout) rootView, azkarIcon, azkarNavItem,
          bottomNavigation, bottomNavigationContainer, btnAzkar, btnDua, btnPrayerTimes, btnQibla,
          btnQuranSuras, currentTime, darkModeIcon, dayText, drawerLayout, homeIcon, homeNavItem,
          lastReadSurah, listenIcon, listenNavItem, menuIcon, moreIcon, moreNavItem, navView,
          notificationIcon, quranIcon, quranNavItem, quranSectionIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
