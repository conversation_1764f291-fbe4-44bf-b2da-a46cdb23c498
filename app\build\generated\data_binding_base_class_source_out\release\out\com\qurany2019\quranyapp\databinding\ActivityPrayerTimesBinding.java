// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.google.android.material.appbar.AppBarLayout;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPrayerTimesBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final AppBarLayout appBar;

  @NonNull
  public final CardView cardAsr;

  @NonNull
  public final CardView cardDhuhr;

  @NonNull
  public final CardView cardFajr;

  @NonNull
  public final CardView cardIsha;

  @NonNull
  public final CardView cardMaghrib;

  @NonNull
  public final CardView cardSunrise;

  @NonNull
  public final Switch switchNotifications;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAsr;

  @NonNull
  public final TextView tvCityName;

  @NonNull
  public final TextView tvCurrentDate;

  @NonNull
  public final TextView tvCurrentTime;

  @NonNull
  public final TextView tvDhuhr;

  @NonNull
  public final TextView tvFajr;

  @NonNull
  public final TextView tvIsha;

  @NonNull
  public final TextView tvMaghrib;

  @NonNull
  public final TextView tvNextPrayer;

  @NonNull
  public final TextView tvNotificationInfo;

  @NonNull
  public final TextView tvSunrise;

  private ActivityPrayerTimesBinding(@NonNull ConstraintLayout rootView, @NonNull AdView adView,
      @NonNull AppBarLayout appBar, @NonNull CardView cardAsr, @NonNull CardView cardDhuhr,
      @NonNull CardView cardFajr, @NonNull CardView cardIsha, @NonNull CardView cardMaghrib,
      @NonNull CardView cardSunrise, @NonNull Switch switchNotifications, @NonNull Toolbar toolbar,
      @NonNull TextView tvAsr, @NonNull TextView tvCityName, @NonNull TextView tvCurrentDate,
      @NonNull TextView tvCurrentTime, @NonNull TextView tvDhuhr, @NonNull TextView tvFajr,
      @NonNull TextView tvIsha, @NonNull TextView tvMaghrib, @NonNull TextView tvNextPrayer,
      @NonNull TextView tvNotificationInfo, @NonNull TextView tvSunrise) {
    this.rootView = rootView;
    this.adView = adView;
    this.appBar = appBar;
    this.cardAsr = cardAsr;
    this.cardDhuhr = cardDhuhr;
    this.cardFajr = cardFajr;
    this.cardIsha = cardIsha;
    this.cardMaghrib = cardMaghrib;
    this.cardSunrise = cardSunrise;
    this.switchNotifications = switchNotifications;
    this.toolbar = toolbar;
    this.tvAsr = tvAsr;
    this.tvCityName = tvCityName;
    this.tvCurrentDate = tvCurrentDate;
    this.tvCurrentTime = tvCurrentTime;
    this.tvDhuhr = tvDhuhr;
    this.tvFajr = tvFajr;
    this.tvIsha = tvIsha;
    this.tvMaghrib = tvMaghrib;
    this.tvNextPrayer = tvNextPrayer;
    this.tvNotificationInfo = tvNotificationInfo;
    this.tvSunrise = tvSunrise;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPrayerTimesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPrayerTimesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_prayer_times, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPrayerTimesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.app_bar;
      AppBarLayout appBar = ViewBindings.findChildViewById(rootView, id);
      if (appBar == null) {
        break missingId;
      }

      id = R.id.cardAsr;
      CardView cardAsr = ViewBindings.findChildViewById(rootView, id);
      if (cardAsr == null) {
        break missingId;
      }

      id = R.id.cardDhuhr;
      CardView cardDhuhr = ViewBindings.findChildViewById(rootView, id);
      if (cardDhuhr == null) {
        break missingId;
      }

      id = R.id.cardFajr;
      CardView cardFajr = ViewBindings.findChildViewById(rootView, id);
      if (cardFajr == null) {
        break missingId;
      }

      id = R.id.cardIsha;
      CardView cardIsha = ViewBindings.findChildViewById(rootView, id);
      if (cardIsha == null) {
        break missingId;
      }

      id = R.id.cardMaghrib;
      CardView cardMaghrib = ViewBindings.findChildViewById(rootView, id);
      if (cardMaghrib == null) {
        break missingId;
      }

      id = R.id.cardSunrise;
      CardView cardSunrise = ViewBindings.findChildViewById(rootView, id);
      if (cardSunrise == null) {
        break missingId;
      }

      id = R.id.switchNotifications;
      Switch switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvAsr;
      TextView tvAsr = ViewBindings.findChildViewById(rootView, id);
      if (tvAsr == null) {
        break missingId;
      }

      id = R.id.tvCityName;
      TextView tvCityName = ViewBindings.findChildViewById(rootView, id);
      if (tvCityName == null) {
        break missingId;
      }

      id = R.id.tvCurrentDate;
      TextView tvCurrentDate = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentDate == null) {
        break missingId;
      }

      id = R.id.tvCurrentTime;
      TextView tvCurrentTime = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTime == null) {
        break missingId;
      }

      id = R.id.tvDhuhr;
      TextView tvDhuhr = ViewBindings.findChildViewById(rootView, id);
      if (tvDhuhr == null) {
        break missingId;
      }

      id = R.id.tvFajr;
      TextView tvFajr = ViewBindings.findChildViewById(rootView, id);
      if (tvFajr == null) {
        break missingId;
      }

      id = R.id.tvIsha;
      TextView tvIsha = ViewBindings.findChildViewById(rootView, id);
      if (tvIsha == null) {
        break missingId;
      }

      id = R.id.tvMaghrib;
      TextView tvMaghrib = ViewBindings.findChildViewById(rootView, id);
      if (tvMaghrib == null) {
        break missingId;
      }

      id = R.id.tvNextPrayer;
      TextView tvNextPrayer = ViewBindings.findChildViewById(rootView, id);
      if (tvNextPrayer == null) {
        break missingId;
      }

      id = R.id.tvNotificationInfo;
      TextView tvNotificationInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvNotificationInfo == null) {
        break missingId;
      }

      id = R.id.tvSunrise;
      TextView tvSunrise = ViewBindings.findChildViewById(rootView, id);
      if (tvSunrise == null) {
        break missingId;
      }

      return new ActivityPrayerTimesBinding((ConstraintLayout) rootView, adView, appBar, cardAsr,
          cardDhuhr, cardFajr, cardIsha, cardMaghrib, cardSunrise, switchNotifications, toolbar,
          tvAsr, tvCityName, tvCurrentDate, tvCurrentTime, tvDhuhr, tvFajr, tvIsha, tvMaghrib,
          tvNextPrayer, tvNotificationInfo, tvSunrise);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
