// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBasicBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final LinearLayout asrLayout;

  @NonNull
  public final TextView asrTime;

  @NonNull
  public final ImageView azkarIcon;

  @NonNull
  public final LinearLayout azkarNavItem;

  @NonNull
  public final LinearLayout azkarSection;

  @NonNull
  public final LinearLayout bottomNavigation;

  @NonNull
  public final RelativeLayout bottomNavigationContainer;

  @NonNull
  public final TextView currentTime;

  @NonNull
  public final TextView dayText;

  @NonNull
  public final LinearLayout dhuhrLayout;

  @NonNull
  public final TextView dhuhrTime;

  @NonNull
  public final LinearLayout fajrLayout;

  @NonNull
  public final TextView fajrTime;

  @NonNull
  public final ImageView homeIcon;

  @NonNull
  public final LinearLayout homeNavItem;

  @NonNull
  public final LinearLayout ishaLayout;

  @NonNull
  public final TextView ishaTime;

  @NonNull
  public final TextView lastReadSurah;

  @NonNull
  public final ImageView listenIcon;

  @NonNull
  public final LinearLayout listenNavItem;

  @NonNull
  public final LinearLayout maghribLayout;

  @NonNull
  public final TextView maghribTime;

  @NonNull
  public final ImageView moreIcon;

  @NonNull
  public final LinearLayout moreNavItem;

  @NonNull
  public final ImageView nightModeIcon;

  @NonNull
  public final TextView nightModeText;

  @NonNull
  public final LinearLayout nightModeToggle;

  @NonNull
  public final LinearLayout prayerTimesSection;

  @NonNull
  public final ImageView quranIcon;

  @NonNull
  public final LinearLayout quranNavItem;

  @NonNull
  public final RelativeLayout quranSection;

  @NonNull
  public final RelativeLayout recitersSection;

  @NonNull
  public final LinearLayout tasbihSection;

  private ActivityMainBasicBinding(@NonNull RelativeLayout rootView, @NonNull AdView adView,
      @NonNull LinearLayout asrLayout, @NonNull TextView asrTime, @NonNull ImageView azkarIcon,
      @NonNull LinearLayout azkarNavItem, @NonNull LinearLayout azkarSection,
      @NonNull LinearLayout bottomNavigation, @NonNull RelativeLayout bottomNavigationContainer,
      @NonNull TextView currentTime, @NonNull TextView dayText, @NonNull LinearLayout dhuhrLayout,
      @NonNull TextView dhuhrTime, @NonNull LinearLayout fajrLayout, @NonNull TextView fajrTime,
      @NonNull ImageView homeIcon, @NonNull LinearLayout homeNavItem,
      @NonNull LinearLayout ishaLayout, @NonNull TextView ishaTime, @NonNull TextView lastReadSurah,
      @NonNull ImageView listenIcon, @NonNull LinearLayout listenNavItem,
      @NonNull LinearLayout maghribLayout, @NonNull TextView maghribTime,
      @NonNull ImageView moreIcon, @NonNull LinearLayout moreNavItem,
      @NonNull ImageView nightModeIcon, @NonNull TextView nightModeText,
      @NonNull LinearLayout nightModeToggle, @NonNull LinearLayout prayerTimesSection,
      @NonNull ImageView quranIcon, @NonNull LinearLayout quranNavItem,
      @NonNull RelativeLayout quranSection, @NonNull RelativeLayout recitersSection,
      @NonNull LinearLayout tasbihSection) {
    this.rootView = rootView;
    this.adView = adView;
    this.asrLayout = asrLayout;
    this.asrTime = asrTime;
    this.azkarIcon = azkarIcon;
    this.azkarNavItem = azkarNavItem;
    this.azkarSection = azkarSection;
    this.bottomNavigation = bottomNavigation;
    this.bottomNavigationContainer = bottomNavigationContainer;
    this.currentTime = currentTime;
    this.dayText = dayText;
    this.dhuhrLayout = dhuhrLayout;
    this.dhuhrTime = dhuhrTime;
    this.fajrLayout = fajrLayout;
    this.fajrTime = fajrTime;
    this.homeIcon = homeIcon;
    this.homeNavItem = homeNavItem;
    this.ishaLayout = ishaLayout;
    this.ishaTime = ishaTime;
    this.lastReadSurah = lastReadSurah;
    this.listenIcon = listenIcon;
    this.listenNavItem = listenNavItem;
    this.maghribLayout = maghribLayout;
    this.maghribTime = maghribTime;
    this.moreIcon = moreIcon;
    this.moreNavItem = moreNavItem;
    this.nightModeIcon = nightModeIcon;
    this.nightModeText = nightModeText;
    this.nightModeToggle = nightModeToggle;
    this.prayerTimesSection = prayerTimesSection;
    this.quranIcon = quranIcon;
    this.quranNavItem = quranNavItem;
    this.quranSection = quranSection;
    this.recitersSection = recitersSection;
    this.tasbihSection = tasbihSection;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBasicBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBasicBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main_basic, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBasicBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.asrLayout;
      LinearLayout asrLayout = ViewBindings.findChildViewById(rootView, id);
      if (asrLayout == null) {
        break missingId;
      }

      id = R.id.asrTime;
      TextView asrTime = ViewBindings.findChildViewById(rootView, id);
      if (asrTime == null) {
        break missingId;
      }

      id = R.id.azkarIcon;
      ImageView azkarIcon = ViewBindings.findChildViewById(rootView, id);
      if (azkarIcon == null) {
        break missingId;
      }

      id = R.id.azkarNavItem;
      LinearLayout azkarNavItem = ViewBindings.findChildViewById(rootView, id);
      if (azkarNavItem == null) {
        break missingId;
      }

      id = R.id.azkarSection;
      LinearLayout azkarSection = ViewBindings.findChildViewById(rootView, id);
      if (azkarSection == null) {
        break missingId;
      }

      id = R.id.bottomNavigation;
      LinearLayout bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.bottomNavigationContainer;
      RelativeLayout bottomNavigationContainer = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigationContainer == null) {
        break missingId;
      }

      id = R.id.currentTime;
      TextView currentTime = ViewBindings.findChildViewById(rootView, id);
      if (currentTime == null) {
        break missingId;
      }

      id = R.id.dayText;
      TextView dayText = ViewBindings.findChildViewById(rootView, id);
      if (dayText == null) {
        break missingId;
      }

      id = R.id.dhuhrLayout;
      LinearLayout dhuhrLayout = ViewBindings.findChildViewById(rootView, id);
      if (dhuhrLayout == null) {
        break missingId;
      }

      id = R.id.dhuhrTime;
      TextView dhuhrTime = ViewBindings.findChildViewById(rootView, id);
      if (dhuhrTime == null) {
        break missingId;
      }

      id = R.id.fajrLayout;
      LinearLayout fajrLayout = ViewBindings.findChildViewById(rootView, id);
      if (fajrLayout == null) {
        break missingId;
      }

      id = R.id.fajrTime;
      TextView fajrTime = ViewBindings.findChildViewById(rootView, id);
      if (fajrTime == null) {
        break missingId;
      }

      id = R.id.homeIcon;
      ImageView homeIcon = ViewBindings.findChildViewById(rootView, id);
      if (homeIcon == null) {
        break missingId;
      }

      id = R.id.homeNavItem;
      LinearLayout homeNavItem = ViewBindings.findChildViewById(rootView, id);
      if (homeNavItem == null) {
        break missingId;
      }

      id = R.id.ishaLayout;
      LinearLayout ishaLayout = ViewBindings.findChildViewById(rootView, id);
      if (ishaLayout == null) {
        break missingId;
      }

      id = R.id.ishaTime;
      TextView ishaTime = ViewBindings.findChildViewById(rootView, id);
      if (ishaTime == null) {
        break missingId;
      }

      id = R.id.lastReadSurah;
      TextView lastReadSurah = ViewBindings.findChildViewById(rootView, id);
      if (lastReadSurah == null) {
        break missingId;
      }

      id = R.id.listenIcon;
      ImageView listenIcon = ViewBindings.findChildViewById(rootView, id);
      if (listenIcon == null) {
        break missingId;
      }

      id = R.id.listenNavItem;
      LinearLayout listenNavItem = ViewBindings.findChildViewById(rootView, id);
      if (listenNavItem == null) {
        break missingId;
      }

      id = R.id.maghribLayout;
      LinearLayout maghribLayout = ViewBindings.findChildViewById(rootView, id);
      if (maghribLayout == null) {
        break missingId;
      }

      id = R.id.maghribTime;
      TextView maghribTime = ViewBindings.findChildViewById(rootView, id);
      if (maghribTime == null) {
        break missingId;
      }

      id = R.id.moreIcon;
      ImageView moreIcon = ViewBindings.findChildViewById(rootView, id);
      if (moreIcon == null) {
        break missingId;
      }

      id = R.id.moreNavItem;
      LinearLayout moreNavItem = ViewBindings.findChildViewById(rootView, id);
      if (moreNavItem == null) {
        break missingId;
      }

      id = R.id.nightModeIcon;
      ImageView nightModeIcon = ViewBindings.findChildViewById(rootView, id);
      if (nightModeIcon == null) {
        break missingId;
      }

      id = R.id.nightModeText;
      TextView nightModeText = ViewBindings.findChildViewById(rootView, id);
      if (nightModeText == null) {
        break missingId;
      }

      id = R.id.nightModeToggle;
      LinearLayout nightModeToggle = ViewBindings.findChildViewById(rootView, id);
      if (nightModeToggle == null) {
        break missingId;
      }

      id = R.id.prayerTimesSection;
      LinearLayout prayerTimesSection = ViewBindings.findChildViewById(rootView, id);
      if (prayerTimesSection == null) {
        break missingId;
      }

      id = R.id.quranIcon;
      ImageView quranIcon = ViewBindings.findChildViewById(rootView, id);
      if (quranIcon == null) {
        break missingId;
      }

      id = R.id.quranNavItem;
      LinearLayout quranNavItem = ViewBindings.findChildViewById(rootView, id);
      if (quranNavItem == null) {
        break missingId;
      }

      id = R.id.quranSection;
      RelativeLayout quranSection = ViewBindings.findChildViewById(rootView, id);
      if (quranSection == null) {
        break missingId;
      }

      id = R.id.recitersSection;
      RelativeLayout recitersSection = ViewBindings.findChildViewById(rootView, id);
      if (recitersSection == null) {
        break missingId;
      }

      id = R.id.tasbihSection;
      LinearLayout tasbihSection = ViewBindings.findChildViewById(rootView, id);
      if (tasbihSection == null) {
        break missingId;
      }

      return new ActivityMainBasicBinding((RelativeLayout) rootView, adView, asrLayout, asrTime,
          azkarIcon, azkarNavItem, azkarSection, bottomNavigation, bottomNavigationContainer,
          currentTime, dayText, dhuhrLayout, dhuhrTime, fajrLayout, fajrTime, homeIcon, homeNavItem,
          ishaLayout, ishaTime, lastReadSurah, listenIcon, listenNavItem, maghribLayout,
          maghribTime, moreIcon, moreNavItem, nightModeIcon, nightModeText, nightModeToggle,
          prayerTimesSection, quranIcon, quranNavItem, quranSection, recitersSection,
          tasbihSection);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
