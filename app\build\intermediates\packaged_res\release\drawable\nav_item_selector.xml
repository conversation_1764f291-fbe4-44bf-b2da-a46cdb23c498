<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- حالة الضغط العصرية -->
    <item android:state_pressed="true">
        <layer-list>
            <!-- خلفية الضغط مع تدرج عصري -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#354CAF50"
                        android:centerColor="#254CAF50"
                        android:endColor="#154CAF50"
                        android:angle="135" />
                    <corners android:radius="22dp" />
                </shape>
            </item>
            <!-- تأثير الإضاءة الداخلية -->
            <item android:top="1dp" android:left="1dp" android:right="1dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#25FFFFFF"
                        android:centerColor="#10FFFFFF"
                        android:endColor="#00FFFFFF"
                        android:angle="90" />
                    <corners android:radius="21dp" />
                </shape>
            </item>
            <!-- نقطة إضاءة مركزية -->
            <item android:top="2dp" android:left="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#15FFFFFF"
                        android:endColor="#00FFFFFF"
                        android:angle="45" />
                    <corners android:radius="20dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- حالة التركيز العصرية -->
    <item android:state_focused="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#204CAF50"
                        android:centerColor="#154CAF50"
                        android:endColor="#0A4CAF50"
                        android:angle="135" />
                    <corners android:radius="22dp" />
                    <stroke
                        android:width="2dp"
                        android:color="#4CAF50" />
                </shape>
            </item>
            <!-- وهج داخلي -->
            <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#10FFFFFF"
                        android:endColor="#00FFFFFF"
                        android:angle="90" />
                    <corners android:radius="20dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- حالة التحديد العصرية (للعنصر النشط) -->
    <item android:state_selected="true">
        <layer-list>
            <!-- خلفية التحديد العصرية -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#304CAF50"
                        android:centerColor="#204CAF50"
                        android:endColor="#104CAF50"
                        android:angle="135" />
                    <corners android:radius="22dp" />
                </shape>
            </item>
            <!-- تأثير الوهج الداخلي -->
            <item android:top="1dp" android:left="1dp" android:right="1dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#20FFFFFF"
                        android:centerColor="#10FFFFFF"
                        android:endColor="#00FFFFFF"
                        android:angle="90" />
                    <corners android:radius="21dp" />
                </shape>
            </item>
            <!-- مؤشر التحديد العصري -->
            <item
                android:gravity="bottom|center_horizontal"
                android:bottom="3dp">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#4CAF50"
                        android:endColor="#66BB6A"
                        android:angle="45" />
                    <size
                        android:width="6dp"
                        android:height="6dp" />
                </shape>
            </item>
            <!-- وهج المؤشر -->
            <item
                android:gravity="bottom|center_horizontal"
                android:bottom="2dp">
                <shape android:shape="oval">
                    <solid android:color="#804CAF50" />
                    <size
                        android:width="10dp"
                        android:height="10dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- الحالة العادية مع تأثير خفيف -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#02000000"
                android:endColor="#00000000"
                android:angle="90" />
            <corners android:radius="22dp" />
        </shape>
    </item>

</selector>
