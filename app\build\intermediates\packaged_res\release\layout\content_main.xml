<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgroundPrimary"
    android:padding="24dp"
    android:gravity="center_horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:orientation="horizontal"
        android:gravity="center">

        <ImageView
            android:id="@+id/btn_quran"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_margin="16dp"
            android:src="@drawable/ic_read"
            android:contentDescription="قراءة"
            android:onClick="read"
            android:background="@color/backgroundCard" />

        <ImageView
            android:id="@+id/btn_listen"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_margin="16dp"
            android:src="@drawable/ic_sound_24dp"
            android:contentDescription="الاستماع"
            android:onClick="listen"
            android:background="@color/backgroundCard" />
    </LinearLayout>
</LinearLayout>
