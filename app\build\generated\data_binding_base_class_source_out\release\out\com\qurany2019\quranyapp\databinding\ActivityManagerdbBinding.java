// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityManagerdbBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final ImageView backgroundCircle;

  @NonNull
  public final ImageView backgroundCrescent;

  @NonNull
  public final ImageButton btnBackward;

  @NonNull
  public final ImageButton btnForward;

  @NonNull
  public final ImageButton btnNext;

  @NonNull
  public final ImageButton btnPlay;

  @NonNull
  public final ImageButton btnPrevious;

  @NonNull
  public final ImageButton btnRepeat;

  @NonNull
  public final ImageButton btnShuffle;

  @NonNull
  public final LinearLayout controlsContainer;

  @NonNull
  public final ImageView decorativeStars;

  @NonNull
  public final LinearLayout extraControlsContainer;

  @NonNull
  public final ImageView innerGlow;

  @NonNull
  public final ImageView islamicPattern1;

  @NonNull
  public final ImageView islamicPattern2;

  @NonNull
  public final LinearLayout layoutads;

  @NonNull
  public final ImageView middleGlow;

  @NonNull
  public final ImageView outerGlow;

  @NonNull
  public final LinearLayout playerHeaderBg;

  @NonNull
  public final LinearLayout progressContainer;

  @NonNull
  public final ImageView quranIcon;

  @NonNull
  public final TextView songCurrentDurationLabel;

  @NonNull
  public final SeekBar songProgressBar;

  @NonNull
  public final FrameLayout songThumbnail;

  @NonNull
  public final TextView songTitle;

  @NonNull
  public final TextView songTotalDurationLabel;

  @NonNull
  public final ImageView star1;

  @NonNull
  public final ImageView star2;

  @NonNull
  public final ImageView star3;

  @NonNull
  public final LinearLayout timerDisplay;

  private ActivityManagerdbBinding(@NonNull RelativeLayout rootView, @NonNull AdView adView,
      @NonNull ImageView backgroundCircle, @NonNull ImageView backgroundCrescent,
      @NonNull ImageButton btnBackward, @NonNull ImageButton btnForward,
      @NonNull ImageButton btnNext, @NonNull ImageButton btnPlay, @NonNull ImageButton btnPrevious,
      @NonNull ImageButton btnRepeat, @NonNull ImageButton btnShuffle,
      @NonNull LinearLayout controlsContainer, @NonNull ImageView decorativeStars,
      @NonNull LinearLayout extraControlsContainer, @NonNull ImageView innerGlow,
      @NonNull ImageView islamicPattern1, @NonNull ImageView islamicPattern2,
      @NonNull LinearLayout layoutads, @NonNull ImageView middleGlow, @NonNull ImageView outerGlow,
      @NonNull LinearLayout playerHeaderBg, @NonNull LinearLayout progressContainer,
      @NonNull ImageView quranIcon, @NonNull TextView songCurrentDurationLabel,
      @NonNull SeekBar songProgressBar, @NonNull FrameLayout songThumbnail,
      @NonNull TextView songTitle, @NonNull TextView songTotalDurationLabel,
      @NonNull ImageView star1, @NonNull ImageView star2, @NonNull ImageView star3,
      @NonNull LinearLayout timerDisplay) {
    this.rootView = rootView;
    this.adView = adView;
    this.backgroundCircle = backgroundCircle;
    this.backgroundCrescent = backgroundCrescent;
    this.btnBackward = btnBackward;
    this.btnForward = btnForward;
    this.btnNext = btnNext;
    this.btnPlay = btnPlay;
    this.btnPrevious = btnPrevious;
    this.btnRepeat = btnRepeat;
    this.btnShuffle = btnShuffle;
    this.controlsContainer = controlsContainer;
    this.decorativeStars = decorativeStars;
    this.extraControlsContainer = extraControlsContainer;
    this.innerGlow = innerGlow;
    this.islamicPattern1 = islamicPattern1;
    this.islamicPattern2 = islamicPattern2;
    this.layoutads = layoutads;
    this.middleGlow = middleGlow;
    this.outerGlow = outerGlow;
    this.playerHeaderBg = playerHeaderBg;
    this.progressContainer = progressContainer;
    this.quranIcon = quranIcon;
    this.songCurrentDurationLabel = songCurrentDurationLabel;
    this.songProgressBar = songProgressBar;
    this.songThumbnail = songThumbnail;
    this.songTitle = songTitle;
    this.songTotalDurationLabel = songTotalDurationLabel;
    this.star1 = star1;
    this.star2 = star2;
    this.star3 = star3;
    this.timerDisplay = timerDisplay;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityManagerdbBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityManagerdbBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_managerdb, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityManagerdbBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.backgroundCircle;
      ImageView backgroundCircle = ViewBindings.findChildViewById(rootView, id);
      if (backgroundCircle == null) {
        break missingId;
      }

      id = R.id.backgroundCrescent;
      ImageView backgroundCrescent = ViewBindings.findChildViewById(rootView, id);
      if (backgroundCrescent == null) {
        break missingId;
      }

      id = R.id.btnBackward;
      ImageButton btnBackward = ViewBindings.findChildViewById(rootView, id);
      if (btnBackward == null) {
        break missingId;
      }

      id = R.id.btnForward;
      ImageButton btnForward = ViewBindings.findChildViewById(rootView, id);
      if (btnForward == null) {
        break missingId;
      }

      id = R.id.btnNext;
      ImageButton btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btnPlay;
      ImageButton btnPlay = ViewBindings.findChildViewById(rootView, id);
      if (btnPlay == null) {
        break missingId;
      }

      id = R.id.btnPrevious;
      ImageButton btnPrevious = ViewBindings.findChildViewById(rootView, id);
      if (btnPrevious == null) {
        break missingId;
      }

      id = R.id.btnRepeat;
      ImageButton btnRepeat = ViewBindings.findChildViewById(rootView, id);
      if (btnRepeat == null) {
        break missingId;
      }

      id = R.id.btnShuffle;
      ImageButton btnShuffle = ViewBindings.findChildViewById(rootView, id);
      if (btnShuffle == null) {
        break missingId;
      }

      id = R.id.controlsContainer;
      LinearLayout controlsContainer = ViewBindings.findChildViewById(rootView, id);
      if (controlsContainer == null) {
        break missingId;
      }

      id = R.id.decorativeStars;
      ImageView decorativeStars = ViewBindings.findChildViewById(rootView, id);
      if (decorativeStars == null) {
        break missingId;
      }

      id = R.id.extraControlsContainer;
      LinearLayout extraControlsContainer = ViewBindings.findChildViewById(rootView, id);
      if (extraControlsContainer == null) {
        break missingId;
      }

      id = R.id.innerGlow;
      ImageView innerGlow = ViewBindings.findChildViewById(rootView, id);
      if (innerGlow == null) {
        break missingId;
      }

      id = R.id.islamicPattern1;
      ImageView islamicPattern1 = ViewBindings.findChildViewById(rootView, id);
      if (islamicPattern1 == null) {
        break missingId;
      }

      id = R.id.islamicPattern2;
      ImageView islamicPattern2 = ViewBindings.findChildViewById(rootView, id);
      if (islamicPattern2 == null) {
        break missingId;
      }

      id = R.id.layoutads;
      LinearLayout layoutads = ViewBindings.findChildViewById(rootView, id);
      if (layoutads == null) {
        break missingId;
      }

      id = R.id.middleGlow;
      ImageView middleGlow = ViewBindings.findChildViewById(rootView, id);
      if (middleGlow == null) {
        break missingId;
      }

      id = R.id.outerGlow;
      ImageView outerGlow = ViewBindings.findChildViewById(rootView, id);
      if (outerGlow == null) {
        break missingId;
      }

      id = R.id.player_header_bg;
      LinearLayout playerHeaderBg = ViewBindings.findChildViewById(rootView, id);
      if (playerHeaderBg == null) {
        break missingId;
      }

      id = R.id.progressContainer;
      LinearLayout progressContainer = ViewBindings.findChildViewById(rootView, id);
      if (progressContainer == null) {
        break missingId;
      }

      id = R.id.quranIcon;
      ImageView quranIcon = ViewBindings.findChildViewById(rootView, id);
      if (quranIcon == null) {
        break missingId;
      }

      id = R.id.songCurrentDurationLabel;
      TextView songCurrentDurationLabel = ViewBindings.findChildViewById(rootView, id);
      if (songCurrentDurationLabel == null) {
        break missingId;
      }

      id = R.id.songProgressBar;
      SeekBar songProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (songProgressBar == null) {
        break missingId;
      }

      id = R.id.songThumbnail;
      FrameLayout songThumbnail = ViewBindings.findChildViewById(rootView, id);
      if (songThumbnail == null) {
        break missingId;
      }

      id = R.id.songTitle;
      TextView songTitle = ViewBindings.findChildViewById(rootView, id);
      if (songTitle == null) {
        break missingId;
      }

      id = R.id.songTotalDurationLabel;
      TextView songTotalDurationLabel = ViewBindings.findChildViewById(rootView, id);
      if (songTotalDurationLabel == null) {
        break missingId;
      }

      id = R.id.star1;
      ImageView star1 = ViewBindings.findChildViewById(rootView, id);
      if (star1 == null) {
        break missingId;
      }

      id = R.id.star2;
      ImageView star2 = ViewBindings.findChildViewById(rootView, id);
      if (star2 == null) {
        break missingId;
      }

      id = R.id.star3;
      ImageView star3 = ViewBindings.findChildViewById(rootView, id);
      if (star3 == null) {
        break missingId;
      }

      id = R.id.timerDisplay;
      LinearLayout timerDisplay = ViewBindings.findChildViewById(rootView, id);
      if (timerDisplay == null) {
        break missingId;
      }

      return new ActivityManagerdbBinding((RelativeLayout) rootView, adView, backgroundCircle,
          backgroundCrescent, btnBackward, btnForward, btnNext, btnPlay, btnPrevious, btnRepeat,
          btnShuffle, controlsContainer, decorativeStars, extraControlsContainer, innerGlow,
          islamicPattern1, islamicPattern2, layoutads, middleGlow, outerGlow, playerHeaderBg,
          progressContainer, quranIcon, songCurrentDurationLabel, songProgressBar, songThumbnail,
          songTitle, songTotalDurationLabel, star1, star2, star3, timerDisplay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
