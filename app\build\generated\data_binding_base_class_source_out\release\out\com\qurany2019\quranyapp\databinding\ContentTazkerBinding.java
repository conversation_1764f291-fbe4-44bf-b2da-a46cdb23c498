// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ContentTazkerBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnNext;

  @NonNull
  public final Button btnPrev;

  @NonNull
  public final Button btnReset;

  @NonNull
  public final Button btnTarget;

  @NonNull
  public final LinearLayout contentTazker;

  @NonNull
  public final Button counter;

  @NonNull
  public final TextView tvCount0;

  @NonNull
  public final TextView tvCount1;

  @NonNull
  public final TextView tvCount2;

  @NonNull
  public final TextView tvCount3;

  @NonNull
  public final TextView tvCount4;

  @NonNull
  public final TextView tvDailyTarget;

  @NonNull
  public final TextView tvTotalCount;

  @NonNull
  public final TextView txtzekr;

  private ContentTazkerBinding(@NonNull ScrollView rootView, @NonNull Button btnNext,
      @NonNull Button btnPrev, @NonNull Button btnReset, @NonNull Button btnTarget,
      @NonNull LinearLayout contentTazker, @NonNull Button counter, @NonNull TextView tvCount0,
      @NonNull TextView tvCount1, @NonNull TextView tvCount2, @NonNull TextView tvCount3,
      @NonNull TextView tvCount4, @NonNull TextView tvDailyTarget, @NonNull TextView tvTotalCount,
      @NonNull TextView txtzekr) {
    this.rootView = rootView;
    this.btnNext = btnNext;
    this.btnPrev = btnPrev;
    this.btnReset = btnReset;
    this.btnTarget = btnTarget;
    this.contentTazker = contentTazker;
    this.counter = counter;
    this.tvCount0 = tvCount0;
    this.tvCount1 = tvCount1;
    this.tvCount2 = tvCount2;
    this.tvCount3 = tvCount3;
    this.tvCount4 = tvCount4;
    this.tvDailyTarget = tvDailyTarget;
    this.tvTotalCount = tvTotalCount;
    this.txtzekr = txtzekr;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ContentTazkerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ContentTazkerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.content_tazker, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ContentTazkerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnNext;
      Button btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btnPrev;
      Button btnPrev = ViewBindings.findChildViewById(rootView, id);
      if (btnPrev == null) {
        break missingId;
      }

      id = R.id.btnReset;
      Button btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.btnTarget;
      Button btnTarget = ViewBindings.findChildViewById(rootView, id);
      if (btnTarget == null) {
        break missingId;
      }

      id = R.id.content_tazker;
      LinearLayout contentTazker = ViewBindings.findChildViewById(rootView, id);
      if (contentTazker == null) {
        break missingId;
      }

      id = R.id.counter;
      Button counter = ViewBindings.findChildViewById(rootView, id);
      if (counter == null) {
        break missingId;
      }

      id = R.id.tvCount0;
      TextView tvCount0 = ViewBindings.findChildViewById(rootView, id);
      if (tvCount0 == null) {
        break missingId;
      }

      id = R.id.tvCount1;
      TextView tvCount1 = ViewBindings.findChildViewById(rootView, id);
      if (tvCount1 == null) {
        break missingId;
      }

      id = R.id.tvCount2;
      TextView tvCount2 = ViewBindings.findChildViewById(rootView, id);
      if (tvCount2 == null) {
        break missingId;
      }

      id = R.id.tvCount3;
      TextView tvCount3 = ViewBindings.findChildViewById(rootView, id);
      if (tvCount3 == null) {
        break missingId;
      }

      id = R.id.tvCount4;
      TextView tvCount4 = ViewBindings.findChildViewById(rootView, id);
      if (tvCount4 == null) {
        break missingId;
      }

      id = R.id.tvDailyTarget;
      TextView tvDailyTarget = ViewBindings.findChildViewById(rootView, id);
      if (tvDailyTarget == null) {
        break missingId;
      }

      id = R.id.tvTotalCount;
      TextView tvTotalCount = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalCount == null) {
        break missingId;
      }

      id = R.id.txtzekr;
      TextView txtzekr = ViewBindings.findChildViewById(rootView, id);
      if (txtzekr == null) {
        break missingId;
      }

      return new ContentTazkerBinding((ScrollView) rootView, btnNext, btnPrev, btnReset, btnTarget,
          contentTazker, counter, tvCount0, tvCount1, tvCount2, tvCount3, tvCount4, tvDailyTarget,
          tvTotalCount, txtzekr);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
