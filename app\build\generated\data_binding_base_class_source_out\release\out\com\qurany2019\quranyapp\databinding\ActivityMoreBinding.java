// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMoreBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final LinearLayout aboutItem;

  @NonNull
  public final LinearLayout backButton;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final LinearLayout hisnItem;

  @NonNull
  public final LinearLayout prayerTimesItem;

  @NonNull
  public final LinearLayout rateItem;

  @NonNull
  public final LinearLayout readersItem;

  @NonNull
  public final LinearLayout settingsItem;

  @NonNull
  public final LinearLayout shareItem;

  @NonNull
  public final LinearLayout tasbihItem;

  @NonNull
  public final TextView tvAppVersion;

  private ActivityMoreBinding(@NonNull RelativeLayout rootView, @NonNull LinearLayout aboutItem,
      @NonNull LinearLayout backButton, @NonNull LinearLayout headerLayout,
      @NonNull LinearLayout hisnItem, @NonNull LinearLayout prayerTimesItem,
      @NonNull LinearLayout rateItem, @NonNull LinearLayout readersItem,
      @NonNull LinearLayout settingsItem, @NonNull LinearLayout shareItem,
      @NonNull LinearLayout tasbihItem, @NonNull TextView tvAppVersion) {
    this.rootView = rootView;
    this.aboutItem = aboutItem;
    this.backButton = backButton;
    this.headerLayout = headerLayout;
    this.hisnItem = hisnItem;
    this.prayerTimesItem = prayerTimesItem;
    this.rateItem = rateItem;
    this.readersItem = readersItem;
    this.settingsItem = settingsItem;
    this.shareItem = shareItem;
    this.tasbihItem = tasbihItem;
    this.tvAppVersion = tvAppVersion;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_more, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.aboutItem;
      LinearLayout aboutItem = ViewBindings.findChildViewById(rootView, id);
      if (aboutItem == null) {
        break missingId;
      }

      id = R.id.backButton;
      LinearLayout backButton = ViewBindings.findChildViewById(rootView, id);
      if (backButton == null) {
        break missingId;
      }

      id = R.id.headerLayout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.hisnItem;
      LinearLayout hisnItem = ViewBindings.findChildViewById(rootView, id);
      if (hisnItem == null) {
        break missingId;
      }

      id = R.id.prayerTimesItem;
      LinearLayout prayerTimesItem = ViewBindings.findChildViewById(rootView, id);
      if (prayerTimesItem == null) {
        break missingId;
      }

      id = R.id.rateItem;
      LinearLayout rateItem = ViewBindings.findChildViewById(rootView, id);
      if (rateItem == null) {
        break missingId;
      }

      id = R.id.readersItem;
      LinearLayout readersItem = ViewBindings.findChildViewById(rootView, id);
      if (readersItem == null) {
        break missingId;
      }

      id = R.id.settingsItem;
      LinearLayout settingsItem = ViewBindings.findChildViewById(rootView, id);
      if (settingsItem == null) {
        break missingId;
      }

      id = R.id.shareItem;
      LinearLayout shareItem = ViewBindings.findChildViewById(rootView, id);
      if (shareItem == null) {
        break missingId;
      }

      id = R.id.tasbihItem;
      LinearLayout tasbihItem = ViewBindings.findChildViewById(rootView, id);
      if (tasbihItem == null) {
        break missingId;
      }

      id = R.id.tvAppVersion;
      TextView tvAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvAppVersion == null) {
        break missingId;
      }

      return new ActivityMoreBinding((RelativeLayout) rootView, aboutItem, backButton, headerLayout,
          hisnItem, prayerTimesItem, rateItem, readersItem, settingsItem, shareItem, tasbihItem,
          tvAppVersion);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
