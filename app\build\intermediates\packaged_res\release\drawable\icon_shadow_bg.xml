<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- الظل -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="oval">
            <solid android:color="#40000000" />
        </shape>
    </item>
    
    <!-- الخلفية الرئيسية -->
    <item android:bottom="2dp" android:right="2dp">
        <selector>
            <!-- حالة الضغط -->
            <item android:state_pressed="true">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#66BB6A"
                        android:endColor="#388E3C"
                        android:angle="135"
                        android:type="linear" />
                    <stroke
                        android:width="2dp"
                        android:color="#FFD700" />
                </shape>
            </item>
            
            <!-- الحالة العادية -->
            <item>
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#4CAF50"
                        android:endColor="#2E7D32"
                        android:angle="135"
                        android:type="linear" />
                    <stroke
                        android:width="1dp"
                        android:color="#81C784" />
                </shape>
            </item>
        </selector>
    </item>
    
</layer-list>
