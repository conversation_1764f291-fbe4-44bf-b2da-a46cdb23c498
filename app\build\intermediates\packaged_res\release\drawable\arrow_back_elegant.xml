<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="?attr/colorControlHighlight">

    <item>
        <vector
            android:width="24dp"
            android:height="24dp"
            android:viewportWidth="24"
            android:viewportHeight="24"
            android:autoMirrored="true">

            <!-- خلفية دائرية أنيقة -->
            <path
                android:fillColor="#15000000"
                android:pathData="M12,12m-11,0a11,11 0,1 1,22 0a11,11 0,1 1,-22 0"/>

            <!-- حدود ذهبية خفيفة -->
            <path
                android:strokeColor="#30D4AF37"
                android:strokeWidth="1"
                android:fillColor="#00000000"
                android:pathData="M12,12m-10.5,0a10.5,10.5 0,1 1,21 0a10.5,10.5 0,1 1,-21 0"/>

            <!-- السهم الرئيسي - متوازن ومتوسط تماماً -->
            <path
                android:fillColor="?attr/colorOnSurface"
                android:pathData="M16,12L8,4l1.4,1.4L15.2,12l-5.8,6.6L8,20z"/>

            <!-- تأثير الإضاءة الداخلية -->
            <path
                android:fillColor="#50FFFFFF"
                android:pathData="M15.2,12L8.8,5.4l0.8,0.8L14.4,12l-4.8,5.8l-0.8,0.8z"/>

        </vector>
    </item>

</ripple>
