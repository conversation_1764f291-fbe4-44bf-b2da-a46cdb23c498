<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/islamic_background"
    tools:context=".AzkarStatisticsActivity">

    <!-- شريط الأدوات -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/islamic_green"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/statistics_title"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- المحتوى الرئيسي -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- بطاقة الإحصائيات اليومية -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/daily_statistics"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamic_green_dark"
                        android:fontFamily="@font/arabic_font"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                    <!-- صف الإحصائيات -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <!-- إجمالي الأذكار -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/totalAzkarCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/islamic_blue"
                                android:fontFamily="@font/arabic_font" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_azkar"
                                android:textSize="12sp"
                                android:textColor="@color/islamic_gray"
                                android:fontFamily="@font/arabic_font" />

                        </LinearLayout>

                        <!-- الأذكار المكتملة -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/completedAzkarCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/islamic_green"
                                android:fontFamily="@font/arabic_font" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/completed_azkar"
                                android:textSize="12sp"
                                android:textColor="@color/islamic_gray"
                                android:fontFamily="@font/arabic_font" />

                        </LinearLayout>

                        <!-- نسبة الإنجاز -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/completionPercentage"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/islamic_gold"
                                android:fontFamily="@font/arabic_font" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/completion_percentage"
                                android:textSize="12sp"
                                android:textColor="@color/islamic_gray"
                                android:fontFamily="@font/arabic_font" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- شريط التقدم الإجمالي -->
                    <ProgressBar
                        android:id="@+id/overallProgressBar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginTop="16dp"
                        android:progressTint="@color/islamic_green"
                        android:progressBackgroundTint="@color/islamic_light_gray" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- بطاقة الرسم البياني -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/weekly_progress"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamic_green_dark"
                        android:fontFamily="@font/arabic_font"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                    <!-- مكان الرسم البياني -->
                    <com.github.mikephil.charting.charts.BarChart
                        android:id="@+id/weeklyChart"
                        android:layout_width="match_parent"
                        android:layout_height="200dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- بطاقة الإحصائيات التفصيلية -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/additional_details"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamic_green_dark"
                        android:fontFamily="@font/arabic_font"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                    <!-- إجمالي التسبيحات -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/total_tasbih_label"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:fontFamily="@font/arabic_font" />

                        <TextView
                            android:id="@+id/totalTasbihCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/islamic_blue"
                            android:fontFamily="@font/arabic_font" />

                    </LinearLayout>

                    <!-- وقت الجلسة -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/session_time_label"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:fontFamily="@font/arabic_font" />

                        <TextView
                            android:id="@+id/sessionDuration"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 دقيقة"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/islamic_green"
                            android:fontFamily="@font/arabic_font" />

                    </LinearLayout>

                    <!-- سلسلة الأيام -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/streak_days_label"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:fontFamily="@font/arabic_font" />

                        <TextView
                            android:id="@+id/streakDays"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 يوم"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/islamic_gold"
                            android:fontFamily="@font/arabic_font" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
