<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_azkar_statistics" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_azkar_statistics.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_azkar_statistics_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="324" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/totalAzkarCount" view="TextView"><Expressions/><location startLine="79" startOffset="28" endLine="87" endOffset="72"/></Target><Target id="@+id/completedAzkarCount" view="TextView"><Expressions/><location startLine="107" startOffset="28" endLine="115" endOffset="72"/></Target><Target id="@+id/completionPercentage" view="TextView"><Expressions/><location startLine="135" startOffset="28" endLine="143" endOffset="72"/></Target><Target id="@+id/overallProgressBar" view="ProgressBar"><Expressions/><location startLine="158" startOffset="20" endLine="165" endOffset="84"/></Target><Target id="@+id/weeklyChart" view="com.github.mikephil.charting.charts.BarChart"><Expressions/><location startLine="198" startOffset="20" endLine="201" endOffset="55"/></Target><Target id="@+id/totalTasbihCount" view="TextView"><Expressions/><location startLine="249" startOffset="24" endLine="257" endOffset="68"/></Target><Target id="@+id/sessionDuration" view="TextView"><Expressions/><location startLine="277" startOffset="24" endLine="285" endOffset="68"/></Target><Target id="@+id/streakDays" view="TextView"><Expressions/><location startLine="304" startOffset="24" endLine="312" endOffset="68"/></Target></Targets></Layout>