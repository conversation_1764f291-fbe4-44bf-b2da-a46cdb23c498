<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- أيقونة القارئ مع تصميم دائري -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="56dp"
            android:layout_height="56dp"
            app:cardCornerRadius="28dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/colorPrimary">

            <ImageView
                android:id="@+id/reciterIcon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center"
                android:src="@drawable/quran_recites"
                android:tint="@android:color/white" />

        </com.google.android.material.card.MaterialCardView>

        <!-- معلومات القارئ -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <!-- اسم القارئ -->
            <TextView
                android:id="@+id/txtRecitesName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="عبد الباسط عبد الصمد"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/textColorPrimary"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="عبد الباسط عبد الصمد" />

            <!-- معلومات إضافية -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_library_music"
                    android:tint="@color/colorAccent" />

                <TextView
                    android:id="@+id/surahCountText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:text="114 سورة"
                    android:textSize="12sp"
                    android:textColor="@color/textColorSecondary"
                    tools:text="114 سورة" />

            </LinearLayout>

        </LinearLayout>

        <!-- زر التشغيل -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/playButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="8dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:insetLeft="0dp"
            android:insetRight="0dp"
            app:icon="@drawable/ic_play_arrow"
            app:iconGravity="textStart"
            app:iconPadding="0dp"
            app:iconSize="24dp"
            app:iconTint="@android:color/white"
            app:backgroundTint="@color/colorPrimary"
            app:cornerRadius="24dp"
            style="@style/Widget.Material3.Button.Icon" />

        <!-- سهم الانتقال -->
        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/backirrow"
            android:tint="@color/textColorSecondary"
            android:alpha="0.6" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
