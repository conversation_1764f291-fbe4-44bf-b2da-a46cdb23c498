<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_achievement" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\item_achievement.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/achievementCardView"><Targets><Target id="@+id/achievementCardView" tag="layout/item_achievement_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="152" endOffset="35"/></Target><Target id="@+id/achievementIcon" view="ImageView"><Expressions/><location startLine="29" startOffset="12" endLine="37" endOffset="39"/></Target><Target id="@+id/lockIcon" view="ImageView"><Expressions/><location startLine="40" startOffset="12" endLine="46" endOffset="43"/></Target><Target id="@+id/achievementTitle" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="67" endOffset="51"/></Target><Target id="@+id/achievementDescription" view="TextView"><Expressions/><location startLine="70" startOffset="12" endLine="78" endOffset="51"/></Target><Target id="@+id/achievementProgressBar" view="ProgressBar"><Expressions/><location startLine="81" startOffset="12" endLine="88" endOffset="76"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="96" startOffset="16" endLine="104" endOffset="60"/></Target><Target id="@+id/rewardPoints" view="TextView"><Expressions/><location startLine="106" startOffset="16" endLine="114" endOffset="60"/></Target><Target id="@+id/statusIcon" view="ImageView"><Expressions/><location startLine="129" startOffset="12" endLine="134" endOffset="43"/></Target><Target id="@+id/unlockDate" view="TextView"><Expressions/><location startLine="137" startOffset="12" endLine="146" endOffset="43"/></Target></Targets></Layout>