package com.qurany2019.quranyapp.database.dao;

import android.database.Cursor;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AzkarStatisticsDao_Impl implements AzkarStatisticsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AzkarStatisticsEntity> __insertionAdapterOfAzkarStatisticsEntity;

  private final EntityDeletionOrUpdateAdapter<AzkarStatisticsEntity> __deletionAdapterOfAzkarStatisticsEntity;

  private final EntityDeletionOrUpdateAdapter<AzkarStatisticsEntity> __updateAdapterOfAzkarStatisticsEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteStatisticsById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteStatisticsByDate;

  private final SharedSQLiteStatement __preparedStmtOfDeleteStatisticsByDateAndCategory;

  private final SharedSQLiteStatement __preparedStmtOfDeleteStatisticsOlderThan;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllStatistics;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDateTimestamp;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCompletionStats;

  private final SharedSQLiteStatement __preparedStmtOfAddTasbihCount;

  private final SharedSQLiteStatement __preparedStmtOfAddSessionDuration;

  private final SharedSQLiteStatement __preparedStmtOfUpdateStreakDays;

  public AzkarStatisticsDao_Impl(RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAzkarStatisticsEntity = new EntityInsertionAdapter<AzkarStatisticsEntity>(__db) {
      @Override
      public String createQuery() {
        return "INSERT OR REPLACE INTO `azkar_statistics_table` (`id`,`date`,`category`,`total_azkar_count`,`completed_azkar_count`,`total_tasbih_count`,`completion_percentage`,`session_duration`,`streak_days`,`best_completion_time`,`average_completion_time`,`created_at`,`updated_at`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AzkarStatisticsEntity value) {
        stmt.bindLong(1, value.getId());
        if (value.getDate() == null) {
          stmt.bindNull(2);
        } else {
          stmt.bindString(2, value.getDate());
        }
        if (value.getCategory() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getCategory());
        }
        stmt.bindLong(4, value.getTotalAzkarCount());
        stmt.bindLong(5, value.getCompletedAzkarCount());
        stmt.bindLong(6, value.getTotalTasbihCount());
        stmt.bindDouble(7, value.getCompletionPercentage());
        stmt.bindLong(8, value.getSessionDuration());
        stmt.bindLong(9, value.getStreakDays());
        stmt.bindLong(10, value.getBestCompletionTime());
        stmt.bindLong(11, value.getAverageCompletionTime());
        stmt.bindLong(12, value.getCreatedAt());
        stmt.bindLong(13, value.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfAzkarStatisticsEntity = new EntityDeletionOrUpdateAdapter<AzkarStatisticsEntity>(__db) {
      @Override
      public String createQuery() {
        return "DELETE FROM `azkar_statistics_table` WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AzkarStatisticsEntity value) {
        stmt.bindLong(1, value.getId());
      }
    };
    this.__updateAdapterOfAzkarStatisticsEntity = new EntityDeletionOrUpdateAdapter<AzkarStatisticsEntity>(__db) {
      @Override
      public String createQuery() {
        return "UPDATE OR ABORT `azkar_statistics_table` SET `id` = ?,`date` = ?,`category` = ?,`total_azkar_count` = ?,`completed_azkar_count` = ?,`total_tasbih_count` = ?,`completion_percentage` = ?,`session_duration` = ?,`streak_days` = ?,`best_completion_time` = ?,`average_completion_time` = ?,`created_at` = ?,`updated_at` = ? WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AzkarStatisticsEntity value) {
        stmt.bindLong(1, value.getId());
        if (value.getDate() == null) {
          stmt.bindNull(2);
        } else {
          stmt.bindString(2, value.getDate());
        }
        if (value.getCategory() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getCategory());
        }
        stmt.bindLong(4, value.getTotalAzkarCount());
        stmt.bindLong(5, value.getCompletedAzkarCount());
        stmt.bindLong(6, value.getTotalTasbihCount());
        stmt.bindDouble(7, value.getCompletionPercentage());
        stmt.bindLong(8, value.getSessionDuration());
        stmt.bindLong(9, value.getStreakDays());
        stmt.bindLong(10, value.getBestCompletionTime());
        stmt.bindLong(11, value.getAverageCompletionTime());
        stmt.bindLong(12, value.getCreatedAt());
        stmt.bindLong(13, value.getUpdatedAt());
        stmt.bindLong(14, value.getId());
      }
    };
    this.__preparedStmtOfDeleteStatisticsById = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_statistics_table WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteStatisticsByDate = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_statistics_table WHERE date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteStatisticsByDateAndCategory = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_statistics_table WHERE date = ? AND category = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteStatisticsOlderThan = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_statistics_table WHERE date < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllStatistics = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_statistics_table";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateDateTimestamp = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_statistics_table SET updated_at = ? WHERE date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCompletionStats = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_statistics_table SET total_azkar_count = ?, completed_azkar_count = ?, completion_percentage = ?, updated_at = ? WHERE date = ? AND category = ?";
        return _query;
      }
    };
    this.__preparedStmtOfAddTasbihCount = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_statistics_table SET total_tasbih_count = total_tasbih_count + ?, updated_at = ? WHERE date = ? AND category = ?";
        return _query;
      }
    };
    this.__preparedStmtOfAddSessionDuration = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_statistics_table SET session_duration = session_duration + ?, updated_at = ? WHERE date = ? AND category = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateStreakDays = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_statistics_table SET streak_days = ?, updated_at = ? WHERE date = ? AND category = ?";
        return _query;
      }
    };
  }

  @Override
  public long insertStatistics(final AzkarStatisticsEntity statistics) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      long _result = __insertionAdapterOfAzkarStatisticsEntity.insertAndReturnId(statistics);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Long> insertAllStatistics(final List<AzkarStatisticsEntity> statisticsList) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      List<Long> _result = __insertionAdapterOfAzkarStatisticsEntity.insertAndReturnIdsList(statisticsList);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int deleteStatistics(final AzkarStatisticsEntity statistics) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total +=__deletionAdapterOfAzkarStatisticsEntity.handle(statistics);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateStatistics(final AzkarStatisticsEntity statistics) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total +=__updateAdapterOfAzkarStatisticsEntity.handle(statistics);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int deleteStatisticsById(final int statisticsId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteStatisticsById.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, statisticsId);
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteStatisticsById.release(_stmt);
    }
  }

  @Override
  public int deleteStatisticsByDate(final String date) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteStatisticsByDate.acquire();
    int _argIndex = 1;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteStatisticsByDate.release(_stmt);
    }
  }

  @Override
  public int deleteStatisticsByDateAndCategory(final String date, final String category) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteStatisticsByDateAndCategory.acquire();
    int _argIndex = 1;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    _argIndex = 2;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteStatisticsByDateAndCategory.release(_stmt);
    }
  }

  @Override
  public int deleteStatisticsOlderThan(final String date) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteStatisticsOlderThan.acquire();
    int _argIndex = 1;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteStatisticsOlderThan.release(_stmt);
    }
  }

  @Override
  public int deleteAllStatistics() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllStatistics.acquire();
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAllStatistics.release(_stmt);
    }
  }

  @Override
  public int updateDateTimestamp(final String date, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDateTimestamp.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateDateTimestamp.release(_stmt);
    }
  }

  @Override
  public int updateCompletionStats(final String date, final String category, final int totalCount,
      final int completedCount, final float percentage, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCompletionStats.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, totalCount);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, completedCount);
    _argIndex = 3;
    _stmt.bindDouble(_argIndex, percentage);
    _argIndex = 4;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 5;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    _argIndex = 6;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateCompletionStats.release(_stmt);
    }
  }

  @Override
  public int addTasbihCount(final String date, final String category, final int count,
      final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfAddTasbihCount.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, count);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 3;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    _argIndex = 4;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfAddTasbihCount.release(_stmt);
    }
  }

  @Override
  public int addSessionDuration(final String date, final String category, final long duration,
      final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfAddSessionDuration.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, duration);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 3;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    _argIndex = 4;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfAddSessionDuration.release(_stmt);
    }
  }

  @Override
  public int updateStreakDays(final String date, final String category, final int streakDays,
      final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateStreakDays.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, streakDays);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 3;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    _argIndex = 4;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateStreakDays.release(_stmt);
    }
  }

  @Override
  public AzkarStatisticsEntity getStatisticsById(final int statisticsId) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, statisticsId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AzkarStatisticsEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _result.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _result.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _result.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _result.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _result.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _result.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _result.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _result.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _result.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _result.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public AzkarStatisticsEntity getStatisticsByDateAndCategory(final String date,
      final String category) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date = ? AND category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    _argIndex = 2;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AzkarStatisticsEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _result.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _result.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _result.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _result.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _result.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _result.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _result.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _result.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _result.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _result.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<AzkarStatisticsEntity> getStatisticsByDateAndCategoryLiveData(final String date,
      final String category) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date = ? AND category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    _argIndex = 2;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_statistics_table"}, false, new Callable<AzkarStatisticsEntity>() {
      @Override
      public AzkarStatisticsEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
          final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
          final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
          final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
          final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
          final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final AzkarStatisticsEntity _result;
          if(_cursor.moveToFirst()) {
            _result = new AzkarStatisticsEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _result.setDate(_tmpDate);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _result.setCategory(_tmpCategory);
            final int _tmpTotalAzkarCount;
            _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
            _result.setTotalAzkarCount(_tmpTotalAzkarCount);
            final int _tmpCompletedAzkarCount;
            _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
            _result.setCompletedAzkarCount(_tmpCompletedAzkarCount);
            final int _tmpTotalTasbihCount;
            _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
            _result.setTotalTasbihCount(_tmpTotalTasbihCount);
            final float _tmpCompletionPercentage;
            _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
            _result.setCompletionPercentage(_tmpCompletionPercentage);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _result.setSessionDuration(_tmpSessionDuration);
            final int _tmpStreakDays;
            _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
            _result.setStreakDays(_tmpStreakDays);
            final long _tmpBestCompletionTime;
            _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
            _result.setBestCompletionTime(_tmpBestCompletionTime);
            final long _tmpAverageCompletionTime;
            _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
            _result.setAverageCompletionTime(_tmpAverageCompletionTime);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result.setUpdatedAt(_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarStatisticsEntity> getStatisticsByDate(final String date) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date = ? ORDER BY category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarStatisticsEntity>> getStatisticsByDateLiveData(final String date) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date = ? ORDER BY category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_statistics_table"}, false, new Callable<List<AzkarStatisticsEntity>>() {
      @Override
      public List<AzkarStatisticsEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
          final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
          final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
          final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
          final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
          final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarStatisticsEntity _item;
            _item = new AzkarStatisticsEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final int _tmpTotalAzkarCount;
            _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
            _item.setTotalAzkarCount(_tmpTotalAzkarCount);
            final int _tmpCompletedAzkarCount;
            _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
            _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
            final int _tmpTotalTasbihCount;
            _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
            _item.setTotalTasbihCount(_tmpTotalTasbihCount);
            final float _tmpCompletionPercentage;
            _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
            _item.setCompletionPercentage(_tmpCompletionPercentage);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final int _tmpStreakDays;
            _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
            _item.setStreakDays(_tmpStreakDays);
            final long _tmpBestCompletionTime;
            _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
            _item.setBestCompletionTime(_tmpBestCompletionTime);
            final long _tmpAverageCompletionTime;
            _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
            _item.setAverageCompletionTime(_tmpAverageCompletionTime);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarStatisticsEntity> getStatisticsByCategory(final String category) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE category = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarStatisticsEntity>> getStatisticsByCategoryLiveData(
      final String category) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE category = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_statistics_table"}, false, new Callable<List<AzkarStatisticsEntity>>() {
      @Override
      public List<AzkarStatisticsEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
          final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
          final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
          final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
          final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
          final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarStatisticsEntity _item;
            _item = new AzkarStatisticsEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final int _tmpTotalAzkarCount;
            _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
            _item.setTotalAzkarCount(_tmpTotalAzkarCount);
            final int _tmpCompletedAzkarCount;
            _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
            _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
            final int _tmpTotalTasbihCount;
            _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
            _item.setTotalTasbihCount(_tmpTotalTasbihCount);
            final float _tmpCompletionPercentage;
            _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
            _item.setCompletionPercentage(_tmpCompletionPercentage);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final int _tmpStreakDays;
            _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
            _item.setStreakDays(_tmpStreakDays);
            final long _tmpBestCompletionTime;
            _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
            _item.setBestCompletionTime(_tmpBestCompletionTime);
            final long _tmpAverageCompletionTime;
            _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
            _item.setAverageCompletionTime(_tmpAverageCompletionTime);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarStatisticsEntity> getAllStatistics() {
    final String _sql = "SELECT * FROM azkar_statistics_table ORDER BY date DESC, category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarStatisticsEntity>> getAllStatisticsLiveData() {
    final String _sql = "SELECT * FROM azkar_statistics_table ORDER BY date DESC, category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_statistics_table"}, false, new Callable<List<AzkarStatisticsEntity>>() {
      @Override
      public List<AzkarStatisticsEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
          final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
          final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
          final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
          final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
          final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarStatisticsEntity _item;
            _item = new AzkarStatisticsEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final int _tmpTotalAzkarCount;
            _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
            _item.setTotalAzkarCount(_tmpTotalAzkarCount);
            final int _tmpCompletedAzkarCount;
            _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
            _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
            final int _tmpTotalTasbihCount;
            _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
            _item.setTotalTasbihCount(_tmpTotalTasbihCount);
            final float _tmpCompletionPercentage;
            _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
            _item.setCompletionPercentage(_tmpCompletionPercentage);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final int _tmpStreakDays;
            _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
            _item.setStreakDays(_tmpStreakDays);
            final long _tmpBestCompletionTime;
            _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
            _item.setBestCompletionTime(_tmpBestCompletionTime);
            final long _tmpAverageCompletionTime;
            _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
            _item.setAverageCompletionTime(_tmpAverageCompletionTime);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarStatisticsEntity> getStatisticsBetweenDates(final String startDate,
      final String endDate) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarStatisticsEntity>> getStatisticsBetweenDatesLiveData(
      final String startDate, final String endDate) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_statistics_table"}, false, new Callable<List<AzkarStatisticsEntity>>() {
      @Override
      public List<AzkarStatisticsEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
          final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
          final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
          final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
          final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
          final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarStatisticsEntity _item;
            _item = new AzkarStatisticsEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final int _tmpTotalAzkarCount;
            _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
            _item.setTotalAzkarCount(_tmpTotalAzkarCount);
            final int _tmpCompletedAzkarCount;
            _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
            _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
            final int _tmpTotalTasbihCount;
            _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
            _item.setTotalTasbihCount(_tmpTotalTasbihCount);
            final float _tmpCompletionPercentage;
            _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
            _item.setCompletionPercentage(_tmpCompletionPercentage);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final int _tmpStreakDays;
            _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
            _item.setStreakDays(_tmpStreakDays);
            final long _tmpBestCompletionTime;
            _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
            _item.setBestCompletionTime(_tmpBestCompletionTime);
            final long _tmpAverageCompletionTime;
            _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
            _item.setAverageCompletionTime(_tmpAverageCompletionTime);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarStatisticsEntity> getStatisticsBetweenDatesAndCategory(final String startDate,
      final String endDate, final String category) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE date BETWEEN ? AND ? AND category = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    _argIndex = 3;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarStatisticsEntity> getFullyCompletedStatistics() {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarStatisticsEntity>> getFullyCompletedStatisticsLiveData() {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_statistics_table"}, false, new Callable<List<AzkarStatisticsEntity>>() {
      @Override
      public List<AzkarStatisticsEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
          final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
          final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
          final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
          final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
          final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarStatisticsEntity _item;
            _item = new AzkarStatisticsEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final int _tmpTotalAzkarCount;
            _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
            _item.setTotalAzkarCount(_tmpTotalAzkarCount);
            final int _tmpCompletedAzkarCount;
            _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
            _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
            final int _tmpTotalTasbihCount;
            _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
            _item.setTotalTasbihCount(_tmpTotalTasbihCount);
            final float _tmpCompletionPercentage;
            _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
            _item.setCompletionPercentage(_tmpCompletionPercentage);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final int _tmpStreakDays;
            _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
            _item.setStreakDays(_tmpStreakDays);
            final long _tmpBestCompletionTime;
            _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
            _item.setBestCompletionTime(_tmpBestCompletionTime);
            final long _tmpAverageCompletionTime;
            _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
            _item.setAverageCompletionTime(_tmpAverageCompletionTime);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getFullyCompletedDaysCount(final String startDate, final String endDate) {
    final String _sql = "SELECT COUNT(*) FROM azkar_statistics_table WHERE completion_percentage = 100.0 AND date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getFullyCompletedDaysCountByCategory(final String startDate, final String endDate,
      final String category) {
    final String _sql = "SELECT COUNT(*) FROM azkar_statistics_table WHERE completion_percentage = 100.0 AND category = ? AND date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    _argIndex = 2;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 3;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getAverageCompletionPercentage(final String startDate, final String endDate) {
    final String _sql = "SELECT AVG(completion_percentage) FROM azkar_statistics_table WHERE date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getAverageCompletionPercentageByCategory(final String startDate,
      final String endDate, final String category) {
    final String _sql = "SELECT AVG(completion_percentage) FROM azkar_statistics_table WHERE date BETWEEN ? AND ? AND category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    _argIndex = 3;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTotalTasbihCount(final String startDate, final String endDate) {
    final String _sql = "SELECT SUM(total_tasbih_count) FROM azkar_statistics_table WHERE date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTotalTasbihCountByCategory(final String startDate, final String endDate,
      final String category) {
    final String _sql = "SELECT SUM(total_tasbih_count) FROM azkar_statistics_table WHERE date BETWEEN ? AND ? AND category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    _argIndex = 3;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public long getTotalSessionDuration(final String startDate, final String endDate) {
    final String _sql = "SELECT SUM(session_duration) FROM azkar_statistics_table WHERE date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final long _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getLong(0);
      } else {
        _result = 0L;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getAverageSessionDuration(final String startDate, final String endDate) {
    final String _sql = "SELECT AVG(session_duration) FROM azkar_statistics_table WHERE date BETWEEN ? AND ? AND session_duration > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public long getBestOverallCompletionTime() {
    final String _sql = "SELECT MIN(best_completion_time) FROM azkar_statistics_table WHERE best_completion_time > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final long _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getLong(0);
      } else {
        _result = 0L;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getBestCompletionPercentage(final String startDate, final String endDate) {
    final String _sql = "SELECT MAX(completion_percentage) FROM azkar_statistics_table WHERE date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getMaxTasbihCountInDay(final String startDate, final String endDate) {
    final String _sql = "SELECT MAX(total_tasbih_count) FROM azkar_statistics_table WHERE date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getMaxStreakDays() {
    final String _sql = "SELECT MAX(streak_days) FROM azkar_statistics_table";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarStatisticsEntity> getRecentStatistics(final int limit) {
    final String _sql = "SELECT * FROM azkar_statistics_table ORDER BY updated_at DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarStatisticsEntity> getRecentCompletions(final int limit) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY updated_at DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<String> getAllCategories() {
    final String _sql = "SELECT DISTINCT category FROM azkar_statistics_table ORDER BY category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<String> getCategoryPerformanceAnalysis(final String startDate, final String endDate) {
    final String _sql = "SELECT category FROM azkar_statistics_table WHERE date BETWEEN ? AND ? GROUP BY category ORDER BY AVG(completion_percentage) DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<String> getFullCompletionDates() {
    final String _sql = "SELECT DISTINCT date FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getFullCompletionDaysCount(final String startDate, final String endDate) {
    final String _sql = "SELECT COUNT(DISTINCT date) FROM azkar_statistics_table WHERE completion_percentage = 100.0 AND date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarStatisticsEntity> getStatisticsOlderThan(final long timestamp) {
    final String _sql = "SELECT * FROM azkar_statistics_table WHERE updated_at < ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, timestamp);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfTotalAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_azkar_count");
      final int _cursorIndexOfCompletedAzkarCount = CursorUtil.getColumnIndexOrThrow(_cursor, "completed_azkar_count");
      final int _cursorIndexOfTotalTasbihCount = CursorUtil.getColumnIndexOrThrow(_cursor, "total_tasbih_count");
      final int _cursorIndexOfCompletionPercentage = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_percentage");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfStreakDays = CursorUtil.getColumnIndexOrThrow(_cursor, "streak_days");
      final int _cursorIndexOfBestCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "best_completion_time");
      final int _cursorIndexOfAverageCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "average_completion_time");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarStatisticsEntity> _result = new ArrayList<AzkarStatisticsEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarStatisticsEntity _item;
        _item = new AzkarStatisticsEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final int _tmpTotalAzkarCount;
        _tmpTotalAzkarCount = _cursor.getInt(_cursorIndexOfTotalAzkarCount);
        _item.setTotalAzkarCount(_tmpTotalAzkarCount);
        final int _tmpCompletedAzkarCount;
        _tmpCompletedAzkarCount = _cursor.getInt(_cursorIndexOfCompletedAzkarCount);
        _item.setCompletedAzkarCount(_tmpCompletedAzkarCount);
        final int _tmpTotalTasbihCount;
        _tmpTotalTasbihCount = _cursor.getInt(_cursorIndexOfTotalTasbihCount);
        _item.setTotalTasbihCount(_tmpTotalTasbihCount);
        final float _tmpCompletionPercentage;
        _tmpCompletionPercentage = _cursor.getFloat(_cursorIndexOfCompletionPercentage);
        _item.setCompletionPercentage(_tmpCompletionPercentage);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final int _tmpStreakDays;
        _tmpStreakDays = _cursor.getInt(_cursorIndexOfStreakDays);
        _item.setStreakDays(_tmpStreakDays);
        final long _tmpBestCompletionTime;
        _tmpBestCompletionTime = _cursor.getLong(_cursorIndexOfBestCompletionTime);
        _item.setBestCompletionTime(_tmpBestCompletionTime);
        final long _tmpAverageCompletionTime;
        _tmpAverageCompletionTime = _cursor.getLong(_cursorIndexOfAverageCompletionTime);
        _item.setAverageCompletionTime(_tmpAverageCompletionTime);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int checkStatisticsExists(final String date, final String category) {
    final String _sql = "SELECT COUNT(*) FROM azkar_statistics_table WHERE date = ? AND category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    _argIndex = 2;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
