<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/islamic_background"
    tools:context=".AchievementsActivity">

    <!-- شريط الأدوات -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/islamic_gold"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:title="@string/achievements_title"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- المحتوى الرئيسي -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- بطاقة ملخص الإنجازات -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/white">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/achievements_summary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamic_gold"
                        android:fontFamily="@font/arabic_font"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                    <!-- صف الإحصائيات -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <!-- إجمالي الإنجازات -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/totalAchievements"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/islamic_blue"
                                android:fontFamily="@font/arabic_font" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/total_achievements"
                                android:textSize="12sp"
                                android:textColor="@color/islamic_gray"
                                android:fontFamily="@font/arabic_font" />

                        </LinearLayout>

                        <!-- الإنجازات المفتوحة -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/unlockedAchievements"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/islamic_green"
                                android:fontFamily="@font/arabic_font" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/unlocked_achievements"
                                android:textSize="12sp"
                                android:textColor="@color/islamic_gray"
                                android:fontFamily="@font/arabic_font" />

                        </LinearLayout>

                        <!-- النقاط -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center">

                            <TextView
                                android:id="@+id/totalPoints"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textSize="24sp"
                                android:textStyle="bold"
                                android:textColor="@color/islamic_gold"
                                android:fontFamily="@font/arabic_font" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/points_text"
                                android:textSize="12sp"
                                android:textColor="@color/islamic_gray"
                                android:fontFamily="@font/arabic_font" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- شريط التقدم الإجمالي -->
                    <ProgressBar
                        android:id="@+id/achievementProgressBar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginTop="16dp"
                        android:progressTint="@color/islamic_gold"
                        android:progressBackgroundTint="@color/islamic_light_gray" />

                    <TextView
                        android:id="@+id/progressText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0% مكتمل"
                        android:textSize="12sp"
                        android:textColor="@color/islamic_gray"
                        android:fontFamily="@font/arabic_font"
                        android:gravity="center"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- تبويبات الإنجازات -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/achievementTabs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@color/white"
                app:tabMode="scrollable"
                app:tabGravity="fill"
                app:tabTextColor="@color/islamic_gray"
                app:tabSelectedTextColor="@color/islamic_gold"
                app:tabIndicatorColor="@color/islamic_gold" />

            <!-- قائمة الإنجازات -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/achievementsRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_achievement" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
