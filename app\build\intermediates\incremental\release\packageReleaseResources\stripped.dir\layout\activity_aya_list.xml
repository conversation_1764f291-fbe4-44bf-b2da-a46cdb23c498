<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@drawable/gradient_background"
    tools:context=".AyaList">

    <!-- Header جميل ومتطور -->
    <RelativeLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:background="@drawable/beautiful_header_background"
        android:elevation="8dp">

        <!-- خلفية زخرفية إسلامية -->
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/islamic_pattern_overlay"
            android:alpha="0.1"
            android:scaleType="centerCrop" />

        <!-- المحتوى الرئيسي -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="20dp">

            <!-- صورة القرآن الجميلة -->
            <RelativeLayout
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginEnd="20dp">

                <!-- خلفية دائرية ذهبية -->
                <View
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:background="@drawable/golden_circle_background" />

                <!-- صورة القرآن -->
                <ImageView
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_centerInParent="true"
                    android:src="@drawable/qurancover"
                    android:scaleType="centerCrop" />

            </RelativeLayout>

            <!-- معلومات القارئ والسور -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/reciterNameText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="القارئ الكريم"
                    android:textColor="@android:color/white"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:fontFamily="serif"
                    android:shadowColor="#40000000"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3" />

                <TextView
                    android:id="@+id/surahCountText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="114 سورة كريمة"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:alpha="0.9"
                    android:fontFamily="serif" />

                <!-- شريط زخرفي -->
                <View
                    android:layout_width="60dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/golden_line" />

            </LinearLayout>

        </LinearLayout>

        <!-- خط زخرفي في الأسفل -->
        <View
            android:layout_width="100dp"
            android:layout_height="3dp"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="16dp"
            android:background="@drawable/golden_line" />

    </RelativeLayout>

    <!-- شريط التنزيل الاحترافي -->
    <LinearLayout
        android:id="@+id/LayoutLoading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/headerLayout"
        android:orientation="vertical"
        android:background="@drawable/download_card_background"
        android:padding="20dp"
        android:layout_margin="16dp"
        android:elevation="6dp"
        android:visibility="gone">

        <!-- أيقونة التنزيل -->
        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:src="@drawable/download_animation"
            android:layout_marginBottom="12dp" />

        <TextView
            android:id="@+id/downloadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="جاري تنزيل السورة الكريمة..."
            android:textColor="@color/colorPrimary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="serif"
            android:layout_marginBottom="16dp" />

        <!-- شريط التقدم الاحترافي -->
        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:max="100"
            android:progressDrawable="@drawable/progress_gradient"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="يرجى الانتظار..."
            android:textColor="@color/colorPrimaryVariant"
            android:textSize="14sp"
            android:fontFamily="serif" />

    </LinearLayout>

    <!-- قائمة السور الرئيسية -->
    <ListView
        android:id="@+id/listView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/headerLayout"
        android:layout_above="@id/adContainer"
        android:divider="@null"
        android:dividerHeight="0dp"
        android:clipToPadding="false"
        android:scrollbarStyle="outsideOverlay"
        android:fadeScrollbars="true"
        android:padding="8dp" />

    <!-- حاوي الإعلانات مع Safe Area -->
    <LinearLayout
        android:id="@+id/adContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:background="@drawable/ad_container_background"
        android:elevation="4dp"
        android:paddingBottom="20dp">

        <!-- خط فاصل جميل -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@drawable/divider_gradient" />

        <com.google.android.gms.ads.AdView
            xmlns:ads="http://schemas.android.com/apk/res-auto"
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="8dp"
            ads:adSize="BANNER"
            ads:adUnitId="@string/banner_ad_id" />

    </LinearLayout>

</RelativeLayout>
