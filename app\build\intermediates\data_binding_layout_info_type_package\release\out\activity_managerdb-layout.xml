<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_managerdb" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_managerdb.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_managerdb_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="406" endOffset="16"/></Target><Target id="@+id/backgroundCrescent" view="ImageView"><Expressions/><location startLine="13" startOffset="4" endLine="23" endOffset="32"/></Target><Target id="@+id/islamicPattern1" view="ImageView"><Expressions/><location startLine="26" startOffset="4" endLine="36" endOffset="32"/></Target><Target id="@+id/islamicPattern2" view="ImageView"><Expressions/><location startLine="39" startOffset="4" endLine="50" endOffset="32"/></Target><Target id="@+id/star1" view="ImageView"><Expressions/><location startLine="53" startOffset="4" endLine="63" endOffset="32"/></Target><Target id="@+id/star2" view="ImageView"><Expressions/><location startLine="65" startOffset="4" endLine="75" endOffset="32"/></Target><Target id="@+id/star3" view="ImageView"><Expressions/><location startLine="77" startOffset="4" endLine="87" endOffset="32"/></Target><Target id="@+id/player_header_bg" view="LinearLayout"><Expressions/><location startLine="90" startOffset="4" endLine="134" endOffset="18"/></Target><Target id="@+id/songTitle" view="TextView"><Expressions/><location startLine="99" startOffset="8" endLine="115" endOffset="42"/></Target><Target id="@+id/songThumbnail" view="FrameLayout"><Expressions/><location startLine="137" startOffset="4" endLine="219" endOffset="17"/></Target><Target id="@+id/backgroundCircle" view="ImageView"><Expressions/><location startLine="152" startOffset="8" endLine="160" endOffset="33"/></Target><Target id="@+id/outerGlow" view="ImageView"><Expressions/><location startLine="165" startOffset="8" endLine="173" endOffset="33"/></Target><Target id="@+id/middleGlow" view="ImageView"><Expressions/><location startLine="176" startOffset="8" endLine="184" endOffset="33"/></Target><Target id="@+id/innerGlow" view="ImageView"><Expressions/><location startLine="187" startOffset="8" endLine="195" endOffset="33"/></Target><Target id="@+id/decorativeStars" view="ImageView"><Expressions/><location startLine="198" startOffset="8" endLine="206" endOffset="33"/></Target><Target id="@+id/quranIcon" view="ImageView"><Expressions/><location startLine="209" startOffset="8" endLine="217" endOffset="34"/></Target><Target id="@+id/progressContainer" view="LinearLayout"><Expressions/><location startLine="222" startOffset="4" endLine="277" endOffset="18"/></Target><Target id="@+id/songProgressBar" view="SeekBar"><Expressions/><location startLine="232" startOffset="8" endLine="244" endOffset="40"/></Target><Target id="@+id/timerDisplay" view="LinearLayout"><Expressions/><location startLine="247" startOffset="8" endLine="275" endOffset="22"/></Target><Target id="@+id/songCurrentDurationLabel" view="TextView"><Expressions/><location startLine="253" startOffset="12" endLine="262" endOffset="41"/></Target><Target id="@+id/songTotalDurationLabel" view="TextView"><Expressions/><location startLine="264" startOffset="12" endLine="273" endOffset="39"/></Target><Target id="@+id/controlsContainer" view="LinearLayout"><Expressions/><location startLine="280" startOffset="4" endLine="345" endOffset="18"/></Target><Target id="@+id/btnPrevious" view="ImageButton"><Expressions/><location startLine="291" startOffset="8" endLine="299" endOffset="37"/></Target><Target id="@+id/btnBackward" view="ImageButton"><Expressions/><location startLine="302" startOffset="8" endLine="310" endOffset="37"/></Target><Target id="@+id/btnPlay" view="ImageButton"><Expressions/><location startLine="313" startOffset="8" endLine="321" endOffset="37"/></Target><Target id="@+id/btnForward" view="ImageButton"><Expressions/><location startLine="324" startOffset="8" endLine="332" endOffset="37"/></Target><Target id="@+id/btnNext" view="ImageButton"><Expressions/><location startLine="335" startOffset="8" endLine="343" endOffset="37"/></Target><Target id="@+id/extraControlsContainer" view="LinearLayout"><Expressions/><location startLine="348" startOffset="4" endLine="381" endOffset="18"/></Target><Target id="@+id/btnRepeat" view="ImageButton"><Expressions/><location startLine="359" startOffset="8" endLine="368" endOffset="37"/></Target><Target id="@+id/btnShuffle" view="ImageButton"><Expressions/><location startLine="370" startOffset="8" endLine="379" endOffset="37"/></Target><Target id="@+id/layoutads" view="LinearLayout"><Expressions/><location startLine="384" startOffset="4" endLine="392" endOffset="18"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="395" startOffset="4" endLine="404" endOffset="45"/></Target></Targets></Layout>