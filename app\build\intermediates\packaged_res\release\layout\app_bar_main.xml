<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        app:title="قرآني"
        app:titleTextColor="@android:color/white"
        app:navigationIconTint="@android:color/white"
        app:popupTheme="@style/ThemeOverlay.Material3.Light" />
</com.google.android.material.appbar.AppBarLayout>
