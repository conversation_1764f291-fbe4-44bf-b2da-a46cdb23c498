// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.google.android.material.appbar.AppBarLayout;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySettingsBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final AppBarLayout appBar;

  @NonNull
  public final LinearLayout arabicLanguageOption;

  @NonNull
  public final ImageView arabicSelectedIcon;

  @NonNull
  public final TextView darkModeDesc;

  @NonNull
  public final ImageView darkModeIcon;

  @NonNull
  public final TextView darkModeTitle;

  @NonNull
  public final LinearLayout englishLanguageOption;

  @NonNull
  public final ImageView englishSelectedIcon;

  @NonNull
  public final Switch switchAutoPlay;

  @NonNull
  public final Switch switchDarkMode;

  @NonNull
  public final Switch switchNotifications;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvFontSize;

  private ActivitySettingsBinding(@NonNull ConstraintLayout rootView, @NonNull AdView adView,
      @NonNull AppBarLayout appBar, @NonNull LinearLayout arabicLanguageOption,
      @NonNull ImageView arabicSelectedIcon, @NonNull TextView darkModeDesc,
      @NonNull ImageView darkModeIcon, @NonNull TextView darkModeTitle,
      @NonNull LinearLayout englishLanguageOption, @NonNull ImageView englishSelectedIcon,
      @NonNull Switch switchAutoPlay, @NonNull Switch switchDarkMode,
      @NonNull Switch switchNotifications, @NonNull Toolbar toolbar, @NonNull TextView tvFontSize) {
    this.rootView = rootView;
    this.adView = adView;
    this.appBar = appBar;
    this.arabicLanguageOption = arabicLanguageOption;
    this.arabicSelectedIcon = arabicSelectedIcon;
    this.darkModeDesc = darkModeDesc;
    this.darkModeIcon = darkModeIcon;
    this.darkModeTitle = darkModeTitle;
    this.englishLanguageOption = englishLanguageOption;
    this.englishSelectedIcon = englishSelectedIcon;
    this.switchAutoPlay = switchAutoPlay;
    this.switchDarkMode = switchDarkMode;
    this.switchNotifications = switchNotifications;
    this.toolbar = toolbar;
    this.tvFontSize = tvFontSize;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.app_bar;
      AppBarLayout appBar = ViewBindings.findChildViewById(rootView, id);
      if (appBar == null) {
        break missingId;
      }

      id = R.id.arabicLanguageOption;
      LinearLayout arabicLanguageOption = ViewBindings.findChildViewById(rootView, id);
      if (arabicLanguageOption == null) {
        break missingId;
      }

      id = R.id.arabicSelectedIcon;
      ImageView arabicSelectedIcon = ViewBindings.findChildViewById(rootView, id);
      if (arabicSelectedIcon == null) {
        break missingId;
      }

      id = R.id.darkModeDesc;
      TextView darkModeDesc = ViewBindings.findChildViewById(rootView, id);
      if (darkModeDesc == null) {
        break missingId;
      }

      id = R.id.darkModeIcon;
      ImageView darkModeIcon = ViewBindings.findChildViewById(rootView, id);
      if (darkModeIcon == null) {
        break missingId;
      }

      id = R.id.darkModeTitle;
      TextView darkModeTitle = ViewBindings.findChildViewById(rootView, id);
      if (darkModeTitle == null) {
        break missingId;
      }

      id = R.id.englishLanguageOption;
      LinearLayout englishLanguageOption = ViewBindings.findChildViewById(rootView, id);
      if (englishLanguageOption == null) {
        break missingId;
      }

      id = R.id.englishSelectedIcon;
      ImageView englishSelectedIcon = ViewBindings.findChildViewById(rootView, id);
      if (englishSelectedIcon == null) {
        break missingId;
      }

      id = R.id.switchAutoPlay;
      Switch switchAutoPlay = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoPlay == null) {
        break missingId;
      }

      id = R.id.switchDarkMode;
      Switch switchDarkMode = ViewBindings.findChildViewById(rootView, id);
      if (switchDarkMode == null) {
        break missingId;
      }

      id = R.id.switchNotifications;
      Switch switchNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchNotifications == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvFontSize;
      TextView tvFontSize = ViewBindings.findChildViewById(rootView, id);
      if (tvFontSize == null) {
        break missingId;
      }

      return new ActivitySettingsBinding((ConstraintLayout) rootView, adView, appBar,
          arabicLanguageOption, arabicSelectedIcon, darkModeDesc, darkModeIcon, darkModeTitle,
          englishLanguageOption, englishSelectedIcon, switchAutoPlay, switchDarkMode,
          switchNotifications, toolbar, tvFontSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
