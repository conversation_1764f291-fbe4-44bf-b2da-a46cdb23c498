<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="com.qurany2019.quranyapp.QiblaActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Theme.QuranyApp.AppBarOverlay"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/Theme.QuranyApp.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/backgroundPrimary"
        app:layout_constraintTop_toBottomOf="@id/app_bar"
        app:layout_constraintBottom_toTopOf="@id/adView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <!-- عنوان الصفحة -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/qibla_title"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/islamicGreenDark"
                android:gravity="center"
                android:layout_marginBottom="32dp" />

            <!-- رسالة فارغة -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="48dp"
                    android:background="@color/white"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/ic_qibla"
                        android:layout_marginBottom="24dp"
                        android:tint="@color/islamicGreenMedium" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="ميزة اتجاه القبلة"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamicGreenDark"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="هذه الميزة قيد التطوير وستكون متاحة قريباً إن شاء الله"
                        android:textSize="16sp"
                        android:textColor="@color/textSecondary"
                        android:gravity="center"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- إعلان جوجل -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-7841751633097845/8640371910"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
