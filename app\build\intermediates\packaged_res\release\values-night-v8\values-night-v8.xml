<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="backgroundCard">#3A3D7A</color>
    <color name="backgroundPrimary">#1A1B3A</color>
    <color name="backgroundSecondary">#2D2F5E</color>
    <color name="black">#FFFFFF</color>
    <color name="bottomNavPurple">#1A1B3A</color>
    <color name="cardPurple">#3A3D7A</color>
    <color name="darkText">#FFFFFF</color>
    <color name="dividerColor">#4A4A4A</color>
    <color name="gradientEnd">#8E44AD</color>
    <color name="gradientStart">#E91E63</color>
    <color name="headerTextColor">#FFFFFF</color>
    <color name="iconTintColor">#1A1A1A</color>
    <color name="islamicBlue">#8E44AD</color>
    <color name="islamicBlueDark">#6A1B9A</color>
    <color name="islamicBlueLight">#BB8FCE</color>
    <color name="islamicGold">#FFD700</color>
    <color name="islamicGoldDark">#FFC107</color>
    <color name="islamicGoldLight">#FFEB3B</color>
    <color name="islamicGreenDark">#8E44AD</color>
    <color name="islamicGreenLight">#BB8FCE</color>
    <color name="islamicGreenMedium">#9B59B6</color>
    <color name="islamicPurpleMedium">#7B1FA2</color>
    <color name="islamicTextPrimary">#E91E63</color>
    <color name="islamic_gold">#FFD700</color>
    <color name="islamic_gray">#B0B0B0</color>
    <color name="islamic_light_gold">#4A148C</color>
    <color name="islamic_light_gray">#3A3A3A</color>
    <color name="islamic_light_green">#2E7D32</color>
    <color name="lavender">#8E44AD</color>
    <color name="lavenderLight">#BB8FCE</color>
    <color name="lightGray">#3A3D7A</color>
    <color name="nightModeCardBg">#FF9800</color>
    <color name="nightModeIconTint">#1A1A1A</color>
    <color name="nightModeTextColor">#1A1A1A</color>
    <color name="pinkLight">#E91E63</color>
    <color name="pinkMedium">#C2185B</color>
    <color name="purpleDark">#6A1B9A</color>
    <color name="purpleLight">#9B59B6</color>
    <color name="purplePrimary">#8E44AD</color>
    <color name="reciterItemBackground">#3A3D7A</color>
    <color name="reciterTextColor">#FFFFFF</color>
    <color name="sectionCardBg">#3A3D7A</color>
    <color name="settingsDarkModeCardBg">#2D2D2D</color>
    <color name="settingsDarkModeIconBg">#FF9800</color>
    <color name="settingsDarkModeIconTint">#1A1A1A</color>
    <color name="switchCardBg">#3A3A3A</color>
    <color name="switchThumbColor">#FF9800</color>
    <color name="switchTrackColor">#4A4A4A</color>
    <color name="textOnPrimary">#FFFFFF</color>
    <color name="textPrimary">#FFFFFF</color>
    <color name="textSecondary">#C8A2C8</color>
    <color name="timeCardBg">#3A3D7A</color>
    <color name="white">#3A3D7A</color>
    <color name="whiteText">#FFFFFF</color>
    <color name="whitebackground">#1A1B3A</color>
    <style name="ArabicText">
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">4dp</item>
        <item name="android:fontFamily">@font/noto</item>
    </style>
    <style name="IslamicButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="backgroundTint">@color/islamicGreenMedium</item>
        <item name="cornerRadius">16dp</item>
    </style>
    <style name="IslamicButtonOutlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="strokeColor">@color/islamicGreenMedium</item>
        <item name="android:textColor">@color/islamicGreenMedium</item>
        <item name="cornerRadius">16dp</item>
    </style>
    <style name="IslamicCardStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="cardBackgroundColor">@color/backgroundCard</item>
    </style>
    <style name="IslamicMainButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginBottom">20dp</item>
        <item name="android:layout_marginStart">24dp</item>
        <item name="android:layout_marginEnd">24dp</item>
        <item name="android:background">@drawable/islamic_ripple_effect</item>
        <item name="android:textColor">@color/textOnPrimary</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:drawablePadding">16dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:paddingTop">16dp</item>
        <item name="android:paddingBottom">16dp</item>
        <item name="android:elevation">6dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    <style name="IslamicMainCard">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:background">@drawable/islamic_card_background</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="IslamicMainTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">42sp</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginTop">32dp</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:fontFamily">@font/noto</item>
        <item name="android:shadowColor">@color/shadowColor</item>
        <item name="android:shadowDx">2</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
    </style>
    <style name="IslamicSubTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/textSecondary</item>
        <item name="android:layout_centerHorizontal">true</item>
        <item name="android:layout_marginBottom">32dp</item>
        <item name="android:fontFamily">@font/noto</item>
    </style>
    <style name="IslamicTitleText">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:gravity">center</item>
        <item name="android:fontFamily">@font/noto</item>
    </style>
    <style name="IslamicToolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/islamicGreenMedium</item>
        <item name="titleTextColor">@color/textOnPrimary</item>
        <item name="subtitleTextColor">@color/textOnPrimary</item>
    </style>
    <style name="QuranButtonText">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/textPrimary</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/noto</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="QuranMainButtonContainer">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:background">@drawable/quran_button_background</item>
        <item name="android:padding">20dp</item>
        <item name="android:layout_marginStart">16dp</item>
        <item name="android:layout_marginEnd">16dp</item>
        <item name="android:elevation">8dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    <style name="Theme" parent="Theme.MaterialComponents.DayNight.NoActionBar"/>
    <style name="Theme.IslamicApp" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/islamicGreenMedium</item>
        <item name="colorPrimaryVariant">@color/islamicGreenDark</item>
        <item name="colorOnPrimary">@color/textOnPrimary</item>
        
        <item name="colorSecondary">@color/islamicGold</item>
        <item name="colorSecondaryVariant">@color/islamicGoldLight</item>
        <item name="colorOnSecondary">@color/textPrimary</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
        <item name="android:windowBackground">@color/backgroundPrimary</item>
        <item name="android:colorBackground">@color/backgroundPrimary</item>
        <item name="colorSurface">@color/backgroundCard</item>
        
    </style>
    <style name="Theme.IslamicApp.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/>
    <style name="Theme.IslamicApp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.IslamicApp.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/>
    <style name="Theme.QuranyApp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">@color/islamicGreenMedium</item>
        <item name="colorPrimaryVariant">@color/islamicGreenDark</item>
        <item name="colorOnPrimary">@color/textOnPrimary</item>
        <item name="colorSecondary">@color/islamicGold</item>
        <item name="colorOnSecondary">@color/textPrimary</item>
        <item name="colorAccent">@color/islamicGold</item>
        <item name="colorSurface">@color/backgroundPrimary</item>
        <item name="colorOnSurface">@color/textPrimary</item>
        <item name="colorSurfaceVariant">@color/backgroundSecondary</item>
        <item name="colorOutline">@color/dividerColor</item>

        <item name="android:statusBarColor">@color/islamicGreenDark</item>
        
        <item name="android:navigationBarColor">@android:color/black</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:fontFamily">@font/noto</item>

        
        <item name="android:windowBackground">@color/backgroundPrimary</item>
        <item name="android:colorBackground">@color/backgroundPrimary</item>
    </style>
    <style name="Theme.QuranyApp.AppBarOverlay" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar"/>
    <style name="Theme.QuranyApp.PopupOverlay" parent="ThemeOverlay.MaterialComponents.Dark"/>
    <style name="ThemeOverlay" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar"/>
    <style name="ayaAr">
        <item name="android:color">#FFDDDDDD</item>
    </style>
    <style name="quranButton">
        <item name="android:textColor">#DDFFFFFF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:padding">8dp</item>
        <item name="android:layout_margin">6dp</item>
        <item name="android:background">@drawable/quran_button</item>
    </style>
    <style name="quran_button">
        <item name="android:textColor">#DDFFFFFF</item>
        <item name="android:textSize">30dp</item>
        <item name="android:padding">4dp</item>
        <item name="android:layout_margin">0dp</item>
        <item name="android:layout_marginLeft">5dp</item>
        <item name="android:layout_marginRight">5dp</item>
        <item name="android:layout_marginTop">5dp</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:background">@drawable/quran_button</item>
    </style>
</resources>