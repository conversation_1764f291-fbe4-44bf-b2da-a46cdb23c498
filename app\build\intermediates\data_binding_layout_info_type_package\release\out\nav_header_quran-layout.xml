<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="nav_header_quran" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\nav_header_quran.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout" rootNodeViewId="@+id/navHeaderContainer"><Targets><Target id="@+id/navHeaderContainer" tag="layout/nav_header_quran_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="105" endOffset="16"/></Target><Target id="@+id/backgroundImageView" view="ImageView"><Expressions/><location startLine="11" startOffset="4" endLine="17" endOffset="41"/></Target><Target id="@+id/iconContainer" view="RelativeLayout"><Expressions/><location startLine="20" startOffset="4" endLine="82" endOffset="20"/></Target><Target id="@+id/appIcon" view="ImageView"><Expressions/><location startLine="54" startOffset="8" endLine="62" endOffset="43"/></Target><Target id="@+id/appName" view="TextView"><Expressions/><location startLine="85" startOffset="4" endLine="101" endOffset="38"/></Target></Targets></Layout>