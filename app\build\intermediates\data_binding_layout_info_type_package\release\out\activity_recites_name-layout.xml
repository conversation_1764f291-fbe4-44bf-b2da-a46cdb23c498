<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_recites_name" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_recites_name.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_recites_name_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="158" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="81" endOffset="53"/></Target><Target id="@+id/collapsingToolbar" view="com.google.android.material.appbar.CollapsingToolbarLayout"><Expressions/><location startLine="17" startOffset="8" endLine="79" endOffset="68"/></Target><Target id="@+id/quranIcon" view="ImageView"><Expressions/><location startLine="34" startOffset="16" endLine="41" endOffset="57"/></Target><Target id="@+id/pageTitle" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="54" endOffset="46"/></Target><Target id="@+id/recitersCountText" view="TextView"><Expressions/><location startLine="57" startOffset="16" endLine="67" endOffset="41"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="72" startOffset="12" endLine="77" endOffset="70"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="107" startOffset="12" endLine="119" endOffset="38"/></Target><Target id="@+id/recitersRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="126" startOffset="4" endLine="132" endOffset="54"/></Target><Target id="@+id/listView" view="ListView"><Expressions/><location startLine="135" startOffset="4" endLine="146" endOffset="70"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="149" startOffset="4" endLine="156" endOffset="45"/></Target></Targets></Layout>