package com.qurany2019.quranyapp.utils;

import android.app.Activity;
import android.os.Build;
import android.view.View;
import android.view.Window;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.graphics.Insets;

/**
 * فئة مساعدة لتطبيق ميزة Edge-to-Edge في التطبيق
 * تدعم جميع إصدارات Android من API 21+
 */
public class EdgeToEdgeHelper {

    /**
     * تفعيل ميزة Edge-to-Edge للنشاط
     * @param activity النشاط المراد تطبيق الميزة عليه
     */
    public static void enableEdgeToEdge(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ - استخدام الطريقة الحديثة
            activity.getWindow().setDecorFitsSystemWindows(false);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // Android 5+ - استخدام WindowCompat
            WindowCompat.setDecorFitsSystemWindows(activity.getWindow(), false);
        }
    }

    /**
     * إعداد Window Insets للعرض الآمن
     * @param activity النشاط
     * @param rootView العنصر الجذر للتخطيط
     */
    public static void setupWindowInsets(Activity activity, View rootView) {
        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            
            // تطبيق padding للمناطق الآمنة
            v.setPadding(
                systemBars.left,
                systemBars.top,
                systemBars.right,
                systemBars.bottom
            );
            
            return insets;
        });
    }

    /**
     * إعداد Window Insets مخصص مع padding انتقائي
     * @param rootView العنصر الجذر للتخطيط
     * @param applyTop تطبيق padding علوي
     * @param applyBottom تطبيق padding سفلي
     * @param applyLeft تطبيق padding يساري
     * @param applyRight تطبيق padding يميني
     */
    public static void setupCustomWindowInsets(View rootView, boolean applyTop, 
                                             boolean applyBottom, boolean applyLeft, boolean applyRight) {
        ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            
            // تطبيق padding انتقائي
            v.setPadding(
                applyLeft ? systemBars.left : 0,
                applyTop ? systemBars.top : 0,
                applyRight ? systemBars.right : 0,
                applyBottom ? systemBars.bottom : 0
            );
            
            return insets;
        });
    }

    /**
     * إعداد Edge-to-Edge شامل للنشاط
     * @param activity النشاط
     * @param rootView العنصر الجذر (اختياري)
     */
    public static void setupEdgeToEdge(Activity activity, View rootView) {
        // تفعيل Edge-to-Edge
        enableEdgeToEdge(activity);
        
        // إعداد Window Insets إذا تم توفير rootView
        if (rootView != null) {
            setupWindowInsets(activity, rootView);
        }
        
        // إعداد ألوان شريط الحالة والتنقل
        setupSystemBarsAppearance(activity);
    }

    /**
     * إعداد Edge-to-Edge مع تحكم مخصص في الـ padding
     * @param activity النشاط
     * @param rootView العنصر الجذر
     * @param applyTop تطبيق padding علوي
     * @param applyBottom تطبيق padding سفلي
     */
    public static void setupEdgeToEdgeCustom(Activity activity, View rootView, 
                                           boolean applyTop, boolean applyBottom) {
        // تفعيل Edge-to-Edge
        enableEdgeToEdge(activity);
        
        // إعداد Window Insets مخصص
        if (rootView != null) {
            setupCustomWindowInsets(rootView, applyTop, applyBottom, false, false);
        }
        
        // إعداد ألوان شريط الحالة والتنقل
        setupSystemBarsAppearance(activity);
    }

    /**
     * إعداد مظهر أشرطة النظام
     * @param activity النشاط
     */
    public static void setupSystemBarsAppearance(Activity activity) {
        Window window = activity.getWindow();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ - استخدام WindowInsetsController
            window.getInsetsController().setSystemBarsAppearance(
                0, // إزالة APPEARANCE_LIGHT_STATUS_BARS لجعل النص أبيض
                android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
            );
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6+ - استخدام systemUiVisibility
            View decorView = window.getDecorView();
            int flags = decorView.getSystemUiVisibility();
            // إزالة FLAG لجعل النص أبيض على خلفية داكنة
            flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            decorView.setSystemUiVisibility(flags);
        }
    }

    /**
     * إعداد مظهر أشرطة النظام للوضع الليلي
     * @param activity النشاط
     * @param isDarkMode هل الوضع الليلي مفعل
     */
    public static void setupSystemBarsForTheme(Activity activity, boolean isDarkMode) {
        Window window = activity.getWindow();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+
            if (isDarkMode) {
                // وضع ليلي - نص أبيض
                window.getInsetsController().setSystemBarsAppearance(
                    0,
                    android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                );
            } else {
                // وضع نهاري - نص أسود
                window.getInsetsController().setSystemBarsAppearance(
                    android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                    android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
                );
            }
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6+
            View decorView = window.getDecorView();
            int flags = decorView.getSystemUiVisibility();
            
            if (isDarkMode) {
                // وضع ليلي - نص أبيض
                flags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            } else {
                // وضع نهاري - نص أسود
                flags |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
            }
            
            decorView.setSystemUiVisibility(flags);
        }
    }

    /**
     * فحص إذا كانت ميزة Edge-to-Edge مدعومة
     * @return true إذا كانت مدعومة
     */
    public static boolean isEdgeToEdgeSupported() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP;
    }

    /**
     * الحصول على ارتفاع شريط الحالة
     * @param activity النشاط
     * @return ارتفاع شريط الحالة بالبكسل
     */
    public static int getStatusBarHeight(Activity activity) {
        int resourceId = activity.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return activity.getResources().getDimensionPixelSize(resourceId);
        }
        return 0;
    }

    /**
     * الحصول على ارتفاع شريط التنقل
     * @param activity النشاط
     * @return ارتفاع شريط التنقل بالبكسل
     */
    public static int getNavigationBarHeight(Activity activity) {
        int resourceId = activity.getResources().getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return activity.getResources().getDimensionPixelSize(resourceId);
        }
        return 0;
    }
}
