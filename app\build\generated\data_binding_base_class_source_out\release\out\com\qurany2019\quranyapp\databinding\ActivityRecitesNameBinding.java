// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityRecitesNameBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final CollapsingToolbarLayout collapsingToolbar;

  @NonNull
  public final ListView listView;

  @NonNull
  public final TextView pageTitle;

  @NonNull
  public final ImageView quranIcon;

  @NonNull
  public final TextView recitersCountText;

  @NonNull
  public final RecyclerView recitersRecyclerView;

  @NonNull
  public final EditText searchEditText;

  @NonNull
  public final Toolbar toolbar;

  private ActivityRecitesNameBinding(@NonNull CoordinatorLayout rootView, @NonNull AdView adView,
      @NonNull AppBarLayout appBarLayout, @NonNull CollapsingToolbarLayout collapsingToolbar,
      @NonNull ListView listView, @NonNull TextView pageTitle, @NonNull ImageView quranIcon,
      @NonNull TextView recitersCountText, @NonNull RecyclerView recitersRecyclerView,
      @NonNull EditText searchEditText, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.adView = adView;
    this.appBarLayout = appBarLayout;
    this.collapsingToolbar = collapsingToolbar;
    this.listView = listView;
    this.pageTitle = pageTitle;
    this.quranIcon = quranIcon;
    this.recitersCountText = recitersCountText;
    this.recitersRecyclerView = recitersRecyclerView;
    this.searchEditText = searchEditText;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityRecitesNameBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityRecitesNameBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_recites_name, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityRecitesNameBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.collapsingToolbar;
      CollapsingToolbarLayout collapsingToolbar = ViewBindings.findChildViewById(rootView, id);
      if (collapsingToolbar == null) {
        break missingId;
      }

      id = R.id.listView;
      ListView listView = ViewBindings.findChildViewById(rootView, id);
      if (listView == null) {
        break missingId;
      }

      id = R.id.pageTitle;
      TextView pageTitle = ViewBindings.findChildViewById(rootView, id);
      if (pageTitle == null) {
        break missingId;
      }

      id = R.id.quranIcon;
      ImageView quranIcon = ViewBindings.findChildViewById(rootView, id);
      if (quranIcon == null) {
        break missingId;
      }

      id = R.id.recitersCountText;
      TextView recitersCountText = ViewBindings.findChildViewById(rootView, id);
      if (recitersCountText == null) {
        break missingId;
      }

      id = R.id.recitersRecyclerView;
      RecyclerView recitersRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recitersRecyclerView == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      EditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityRecitesNameBinding((CoordinatorLayout) rootView, adView, appBarLayout,
          collapsingToolbar, listView, pageTitle, quranIcon, recitersCountText,
          recitersRecyclerView, searchEditText, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
