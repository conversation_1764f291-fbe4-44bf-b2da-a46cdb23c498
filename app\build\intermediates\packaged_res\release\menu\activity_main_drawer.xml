<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <!-- الواجهة الرئيسية -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_islamic"
        android:padding="16dp">

        <TextView
            android:id="@+id/appTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="قرآني"
            android:textColor="@color/green"
            android:textSize="32sp"
            android:textStyle="bold"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="32dp"
            android:fontFamily="sans-serif" />

        <LinearLayout
            android:id="@+id/main_buttons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="32dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="3"
            android:layout_centerHorizontal="true">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/rounded_button"
                android:padding="12dp"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="openSuras">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_read"
                    android:tint="@color/green" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="سور القرآن"
                    android:textColor="@color/green"
                    android:textSize="14sp"
                    android:layout_marginTop="4dp"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/rounded_button"
                android:padding="12dp"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="openListening">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_sound_24dp"
                    android:tint="@color/green" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الاستماع"
                    android:textColor="@color/green"
                    android:textSize="14sp"
                    android:layout_marginTop="4dp"/>
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/rounded_button"
                android:padding="12dp"
                android:clickable="true"
                android:foreground="?attr/selectableItemBackground"
                android:onClick="openMoreApps">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_menu"
                    android:tint="@color/green" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="المزيد"
                    android:textColor="@color/green"
                    android:textSize="14sp"
                    android:layout_marginTop="4dp"/>
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>

    <!-- القائمة الجانبية -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:background="@android:color/white"
        app:headerLayout="@layout/nav_header_main"
        app:menu="@menu/activity_main_drawer" />

</androidx.drawerlayout.widget.DrawerLayout>
