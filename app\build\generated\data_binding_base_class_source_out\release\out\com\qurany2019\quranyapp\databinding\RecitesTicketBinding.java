// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class RecitesTicketBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout cardLayout;

  @NonNull
  public final TextView txtRecitesName;

  private RecitesTicketBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout cardLayout,
      @NonNull TextView txtRecitesName) {
    this.rootView = rootView;
    this.cardLayout = cardLayout;
    this.txtRecitesName = txtRecitesName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static RecitesTicketBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static RecitesTicketBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.recites_ticket, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static RecitesTicketBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.card_layout;
      LinearLayout cardLayout = ViewBindings.findChildViewById(rootView, id);
      if (cardLayout == null) {
        break missingId;
      }

      id = R.id.txtRecitesName;
      TextView txtRecitesName = ViewBindings.findChildViewById(rootView, id);
      if (txtRecitesName == null) {
        break missingId;
      }

      return new RecitesTicketBinding((LinearLayout) rootView, cardLayout, txtRecitesName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
