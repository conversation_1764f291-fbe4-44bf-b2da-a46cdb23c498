<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main_new" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_main_new.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_new_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="771" endOffset="43"/></Target><Target id="@+id/menuIcon" view="ImageView"><Expressions/><location startLine="37" startOffset="20" endLine="45" endOffset="52"/></Target><Target id="@+id/darkModeIcon" view="ImageView"><Expressions/><location startLine="66" startOffset="24" endLine="72" endOffset="56"/></Target><Target id="@+id/notificationIcon" view="ImageView"><Expressions/><location startLine="74" startOffset="24" endLine="79" endOffset="56"/></Target><Target id="@+id/dayText" view="TextView"><Expressions/><location startLine="100" startOffset="24" endLine="110" endOffset="61"/></Target><Target id="@+id/currentTime" view="TextView"><Expressions/><location startLine="113" startOffset="24" endLine="122" endOffset="61"/></Target><Target id="@+id/lastReadSurah" view="TextView"><Expressions/><location startLine="140" startOffset="28" endLine="148" endOffset="70"/></Target><Target id="@+id/btnQuranSuras" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="354" startOffset="20" endLine="391" endOffset="55"/></Target><Target id="@+id/quranSectionIcon" view="ImageView"><Expressions/><location startLine="369" startOffset="28" endLine="376" endOffset="65"/></Target><Target id="@+id/btnAzkar" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="394" startOffset="20" endLine="430" endOffset="55"/></Target><Target id="@+id/btnDua" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="444" startOffset="20" endLine="480" endOffset="55"/></Target><Target id="@+id/btnPrayerTimes" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="483" startOffset="20" endLine="519" endOffset="55"/></Target><Target id="@+id/btnQibla" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="524" startOffset="16" endLine="560" endOffset="51"/></Target><Target id="@+id/bottomNavigationContainer" view="RelativeLayout"><Expressions/><location startLine="567" startOffset="8" endLine="754" endOffset="24"/></Target><Target id="@+id/bottomNavigation" view="LinearLayout"><Expressions/><location startLine="579" startOffset="12" endLine="752" endOffset="26"/></Target><Target id="@+id/homeNavItem" view="LinearLayout"><Expressions/><location startLine="593" startOffset="16" endLine="622" endOffset="30"/></Target><Target id="@+id/homeIcon" view="ImageView"><Expressions/><location startLine="605" startOffset="20" endLine="611" endOffset="58"/></Target><Target id="@+id/quranNavItem" view="LinearLayout"><Expressions/><location startLine="625" startOffset="16" endLine="654" endOffset="30"/></Target><Target id="@+id/quranIcon" view="ImageView"><Expressions/><location startLine="637" startOffset="20" endLine="643" endOffset="58"/></Target><Target id="@+id/listenNavItem" view="LinearLayout"><Expressions/><location startLine="657" startOffset="16" endLine="686" endOffset="30"/></Target><Target id="@+id/listenIcon" view="ImageView"><Expressions/><location startLine="669" startOffset="20" endLine="675" endOffset="58"/></Target><Target id="@+id/azkarNavItem" view="LinearLayout"><Expressions/><location startLine="689" startOffset="16" endLine="718" endOffset="30"/></Target><Target id="@+id/azkarIcon" view="ImageView"><Expressions/><location startLine="701" startOffset="20" endLine="707" endOffset="58"/></Target><Target id="@+id/moreNavItem" view="LinearLayout"><Expressions/><location startLine="721" startOffset="16" endLine="750" endOffset="30"/></Target><Target id="@+id/moreIcon" view="ImageView"><Expressions/><location startLine="733" startOffset="20" endLine="739" endOffset="58"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="759" startOffset="4" endLine="769" endOffset="54"/></Target></Targets></Layout>