<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/backgroundSecondary" />
                    <corners android:radius="12dp" />
                    <stroke
                        android:width="1dp"
                        android:color="@color/islamicGreenLight" />
                </shape>
            </item>
            <item android:left="4dp">
                <shape android:shape="rectangle">
                    <solid android:color="#10000000" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/backgroundSecondary" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/islamic_light_gray" />
        </shape>
    </item>

</selector>
