<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    tools:context=".RecitesName">

    <!-- AppBar مع تأثير جميل -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="@color/colorPrimary"
            app:expandedTitleMarginStart="16dp"
            app:expandedTitleMarginEnd="64dp">

            <!-- خلفية متدرجة مع أيقونة -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/header_gradient"
                app:layout_collapseMode="parallax">

                <!-- أيقونة القرآن مع انيميشن -->
                <ImageView
                    android:id="@+id/quranIcon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="40dp"
                    android:src="@drawable/ic_quran_modern"
                    android:tint="@android:color/white" />

                <!-- عنوان الصفحة -->
                <TextView
                    android:id="@+id/pageTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/quranIcon"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="8dp"
                    android:text="@string/reciters_list_title"
                    android:textColor="@color/textOnPrimary"
                    android:textSize="22sp"
                    android:textStyle="bold" />

                <!-- عدد القراء -->
                <TextView
                    android:id="@+id/recitersCountText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/pageTitle"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="4dp"
                    android:text="@string/reciters_count_text"
                    android:textColor="@color/textOnPrimary"
                    android:textSize="14sp"
                    android:alpha="0.8" />

            </RelativeLayout>

            <!-- Toolbar -->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- بطاقة البحث -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="12dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_search"
                android:tint="@color/islamicBlue"
                android:layout_marginEnd="12dp" />

            <EditText
                android:id="@+id/searchEditText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="@string/search_reciter_hint"
                android:background="@null"
                android:textSize="16sp"
                android:textColorHint="@color/textSecondary"
                android:textColor="@color/textPrimary"
                android:inputType="text"
                android:imeOptions="actionSearch"
                android:maxLines="1" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- قائمة القراء (مخفية مؤقتاً) -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recitersRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false"
        android:visibility="gone"
        tools:listitem="@layout/item_reciter_modern" />

    <!-- ListView القديم (مرئي للتوافق مع الكود القديم) -->
    <ListView
        android:id="@+id/listView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="80dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="120dp"
        android:clipToPadding="false"
        android:paddingBottom="40dp"
        android:visibility="visible"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <!-- الإعلانات -->
    <com.google.android.gms.ads.AdView
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        ads:adSize="SMART_BANNER"
        ads:adUnitId="@string/banner_ad_id" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
