<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backgroundPrimary"
    tools:context=".About">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="24dp"
        android:paddingEnd="24dp"
        android:paddingBottom="24dp"
        android:paddingTop="80dp">

        <!-- Header Section -->
        <!-- بطاقة تعريف أنيقة للتطبيق -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="12dp">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="180dp"
                android:background="@drawable/elegant_app_card_background">

                <!-- زخرفة إسلامية في الخلفية -->
                <ImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="-20dp"
                    android:src="@drawable/islamic_pattern_light"
                    android:alpha="0.1"
                    android:scaleType="centerCrop" />

                <!-- المحتوى الرئيسي -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:padding="24dp"
                    android:gravity="center_vertical">

                    <!-- الجانب الأيسر: الأيقونة المكبرة مع تأثيرات جميلة -->
                    <RelativeLayout
                        android:layout_width="130dp"
                        android:layout_height="130dp"
                        android:layout_marginEnd="20dp">

                        <!-- ظل خلفي متحرك -->
                        <View
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:layout_centerInParent="true"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/floating_shadow"
                            android:alpha="0.3" />

                        <!-- دائرة خلفية متوهجة -->
                        <View
                            android:layout_width="125dp"
                            android:layout_height="125dp"
                            android:layout_centerInParent="true"
                            android:background="@drawable/glowing_circle"
                            android:elevation="2dp"
                            android:alpha="0.6" />

                        <!-- خلفية الأيقونة الرئيسية -->
                        <View
                            android:layout_width="115dp"
                            android:layout_height="115dp"
                            android:layout_centerInParent="true"
                            android:background="@drawable/premium_icon_container"
                            android:elevation="4dp" />

                        <!-- أيقونة التطبيق بنفس حجم المربع الأبيض -->
                        <ImageView
                            android:layout_width="115dp"
                            android:layout_height="115dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/ic_app_logo"
                            android:padding="5dp"
                            android:elevation="8dp"
                            android:scaleType="fitCenter" />

                        <!-- إطار ذهبي متدرج -->
                        <View
                            android:layout_width="115dp"
                            android:layout_height="115dp"
                            android:layout_centerInParent="true"
                            android:background="@drawable/gradient_golden_border"
                            android:elevation="6dp" />

                        <!-- نجمة زخرفية علوية -->
                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="4dp"
                            android:src="@drawable/decorative_star"
                            android:elevation="10dp"
                            android:tint="@color/islamicGold" />

                        <!-- نقاط زخرفية جانبية -->
                        <View
                            android:layout_width="6dp"
                            android:layout_height="6dp"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="2dp"
                            android:background="@drawable/golden_dot"
                            android:elevation="9dp" />

                        <View
                            android:layout_width="6dp"
                            android:layout_height="6dp"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="2dp"
                            android:background="@drawable/golden_dot"
                            android:elevation="9dp" />

                    </RelativeLayout>

                    <!-- الجانب الأيمن: المعلومات -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <!-- اسم التطبيق -->
                        <TextView
                            android:id="@+id/app_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_name"
                            android:textSize="28sp"
                            android:textStyle="bold"
                            android:textColor="@color/white"
                            android:fontFamily="@font/noto"
                            android:shadowColor="@color/black"
                            android:shadowDx="2"
                            android:shadowDy="2"
                            android:shadowRadius="4"
                            android:layout_marginBottom="8dp" />

                        <!-- خط فاصل ذهبي -->
                        <View
                            android:layout_width="60dp"
                            android:layout_height="2dp"
                            android:background="@color/islamicGold"
                            android:layout_marginBottom="8dp" />

                        <!-- نسخة التطبيق -->
                        <TextView
                            android:id="@+id/app_version_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_version"
                            android:textSize="16sp"
                            android:textColor="@color/white"
                            android:alpha="0.9"
                            android:layout_marginBottom="4dp" />

                        <!-- وصف مختصر -->
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/app_title_about"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:alpha="0.8"
                            android:fontStyle="italic" />

                    </LinearLayout>

                </LinearLayout>

                <!-- زخرفة إسلامية في الزاوية -->
                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_alignParentStart="true"
                    android:layout_alignParentBottom="true"
                    android:layout_margin="16dp"
                    android:src="@drawable/islamic_corner_decoration"
                    android:tint="@color/islamicGold"
                    android:alpha="0.6" />

            </RelativeLayout>
        </androidx.cardview.widget.CardView>

        <!-- آية قرآنية -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:background="@color/white">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/islamic_decoration"
                    android:layout_gravity="center"
                    android:layout_marginBottom="16dp"
                    android:tint="@color/islamicGold" />

                <TextView
                    android:id="@+id/quran_verse"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/quran_verse_about"
                    android:textSize="18sp"
                    android:textColor="@color/islamicGreenDark"
                    android:gravity="center"
                    android:fontFamily="@font/noto"
                    android:lineSpacingExtra="8dp"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/verse_reference"
                    android:textSize="14sp"
                    android:textColor="@color/textSecondary"
                    android:gravity="center"
                    android:fontStyle="italic" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- وصف التطبيق -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:background="@color/white">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/about_app_title"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamicGreenDark"
                    android:layout_marginBottom="16dp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/app_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/app_description_new"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:lineSpacingExtra="4dp"
                    android:gravity="center" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- ميزات التطبيق -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:background="@color/white">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/app_features_title"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamicGreenDark"
                    android:layout_marginBottom="16dp"
                    android:gravity="center" />

                <!-- قائمة الميزات -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- ميزة 1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_quran_listen"
                            android:layout_marginEnd="16dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/feature_listen_desc"
                            android:textSize="16sp"
                            android:textColor="@color/textPrimary" />

                    </LinearLayout>

                    <!-- ميزة 2 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_quran_read"
                            android:layout_marginEnd="16dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/feature_read_desc"
                            android:textSize="16sp"
                            android:textColor="@color/textPrimary" />

                    </LinearLayout>

                    <!-- ميزة 3 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_tasbih"
                            android:layout_marginEnd="16dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/feature_tasbih_desc"
                            android:textSize="16sp"
                            android:textColor="@color/textPrimary" />

                    </LinearLayout>

                    <!-- ميزة 4 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_dua"
                            android:layout_marginEnd="16dp" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/feature_azkar_desc"
                            android:textSize="16sp"
                            android:textColor="@color/textPrimary" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- معلومات المطور -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:background="@color/white">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/developer_info_title"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamicGreenDark"
                    android:layout_marginBottom="16dp"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/developer_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/developer_name"
                    android:textSize="18sp"
                    android:textColor="@color/textPrimary"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/developer_message"
                    android:textSize="14sp"
                    android:textColor="@color/textSecondary"
                    android:gravity="center"
                    android:fontStyle="italic" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- قسم الروابط المهمة -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:background="@drawable/gradient_background_light"
                android:padding="28dp">

                <!-- العنوان مع تصميم جميل -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="24dp">

                    <View
                        android:layout_width="40dp"
                        android:layout_height="2dp"
                        android:background="@color/islamicGoldDark"
                        android:layout_gravity="center_vertical" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/important_links_title"
                        android:textSize="22sp"
                        android:textStyle="bold"
                        android:textColor="@color/islamicGreenDark"
                        android:layout_marginHorizontal="16dp"
                        android:gravity="center" />

                    <View
                        android:layout_width="40dp"
                        android:layout_height="2dp"
                        android:background="@color/islamicGoldDark"
                        android:layout_gravity="center_vertical" />

                </LinearLayout>

                <!-- كارت سياسة الخصوصية -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/privacyPolicySection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:background="@drawable/gradient_privacy_background">

                        <!-- أيقونة مع خلفية دائرية -->
                        <LinearLayout
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:background="@drawable/circle_background_privacy"
                            android:gravity="center"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🔒"
                                android:textSize="24sp"
                                android:gravity="center" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/privacy_policy_title"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:shadowColor="#80000000"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="2" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/privacy_policy_desc"
                                android:textSize="14sp"
                                android:textColor="#E8F5E8"
                                android:layout_marginTop="4dp"
                                android:lineSpacingExtra="2dp" />

                        </LinearLayout>

                        <!-- سهم مع تأثير -->
                        <LinearLayout
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:background="@drawable/circle_background_arrow"
                            android:gravity="center"
                            android:layout_gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="→"
                                android:textSize="18sp"
                                android:textColor="@color/islamicGreenDark"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- كارت المزيد من التطبيقات -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/moreAppsSection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="20dp"
                        android:background="@drawable/gradient_apps_background">

                        <!-- أيقونة مع خلفية دائرية -->
                        <LinearLayout
                            android:layout_width="56dp"
                            android:layout_height="56dp"
                            android:background="@drawable/circle_background_apps"
                            android:gravity="center"
                            android:layout_gravity="center_vertical"
                            android:layout_marginEnd="16dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📱"
                                android:textSize="24sp"
                                android:gravity="center" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/more_apps_title"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/white"
                                android:shadowColor="#80000000"
                                android:shadowDx="1"
                                android:shadowDy="1"
                                android:shadowRadius="2" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/more_apps_desc"
                                android:textSize="14sp"
                                android:textColor="#E8F5E8"
                                android:layout_marginTop="4dp"
                                android:lineSpacingExtra="2dp" />

                        </LinearLayout>

                        <!-- سهم مع تأثير -->
                        <LinearLayout
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:background="@drawable/circle_background_arrow"
                            android:gravity="center"
                            android:layout_gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="→"
                                android:textSize="18sp"
                                android:textColor="@color/islamicGreenDark"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- إعلان جوجل -->
        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:adSize="BANNER"
            app:adUnitId="ca-app-pub-7841751633097845/8640371910" />

    </LinearLayout>

</ScrollView>
