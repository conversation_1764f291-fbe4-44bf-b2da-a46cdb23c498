<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_pressed="true" >
        <shape>
            <corners android:radius="10dp"></corners>
            <solid
                android:color="?attr/colorPrimaryVariant" />

        </shape>
    </item>


    <item>
        <shape>
            <corners android:radius="10dp"></corners>
            <solid
                android:color="?attr/colorSurface" />
            <stroke
                android:width="1dp"
                android:color="?attr/colorOutline" />

        </shape>
    </item>
</selector>