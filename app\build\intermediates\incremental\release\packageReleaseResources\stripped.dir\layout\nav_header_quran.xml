<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/navHeaderContainer"
    android:layout_width="match_parent"
    android:layout_height="220dp"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <!-- خلفية النهار/الليل كاملة ومناسبة -->
    <ImageView
        android:id="@+id/backgroundImageView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/bg_img_night"
        android:scaleType="centerInside"
        android:adjustViewBounds="true" />

    <!-- أيقونة التطبيق المكبرة مع تأثيرات جميلة -->
    <RelativeLayout
        android:id="@+id/iconContainer"
        android:layout_width="110dp"
        android:layout_height="110dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="20dp">

        <!-- ظل خلفي -->
        <View
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_centerInParent="true"
            android:layout_marginTop="6dp"
            android:background="@drawable/floating_shadow"
            android:alpha="0.2" />

        <!-- دائرة متوهجة -->
        <View
            android:layout_width="105dp"
            android:layout_height="105dp"
            android:layout_centerInParent="true"
            android:background="@drawable/glowing_circle"
            android:elevation="2dp"
            android:alpha="0.5" />

        <!-- خلفية الأيقونة -->
        <View
            android:layout_width="95dp"
            android:layout_height="95dp"
            android:layout_centerInParent="true"
            android:background="@drawable/premium_icon_container"
            android:elevation="3dp" />

        <!-- أيقونة التطبيق بنفس حجم المربع الأبيض -->
        <ImageView
            android:id="@+id/appIcon"
            android:layout_width="95dp"
            android:layout_height="95dp"
            android:layout_centerInParent="true"
            android:src="@drawable/ic_app_logo"
            android:padding="4dp"
            android:elevation="5dp"
            android:scaleType="fitCenter" />

        <!-- إطار ذهبي متدرج -->
        <View
            android:layout_width="95dp"
            android:layout_height="95dp"
            android:layout_centerInParent="true"
            android:background="@drawable/gradient_golden_border"
            android:elevation="4dp" />

        <!-- نجمة زخرفية -->
        <ImageView
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="3dp"
            android:src="@drawable/decorative_star"
            android:elevation="6dp"
            android:tint="@color/islamicGold" />

    </RelativeLayout>

    <!-- اسم التطبيق -->
    <TextView
        android:id="@+id/appName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/iconContainer"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="8dp"
        android:text="قرآني"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        android:fontFamily="@font/noto"
        android:shadowColor="@color/black"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2"
        android:visibility="visible" />

    <!-- حذف وصف التطبيق والزخرفة السفلية -->

</RelativeLayout>
