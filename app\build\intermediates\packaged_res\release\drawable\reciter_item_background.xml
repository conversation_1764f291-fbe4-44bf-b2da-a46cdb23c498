<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="?attr/colorControlHighlight">
    
    <item>
        <layer-list>
            <!-- الخلفية الأساسية -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="?attr/colorSurface" />
                    <corners android:radius="8dp" />
                    <stroke
                        android:width="1dp"
                        android:color="?attr/colorOutline" />
                </shape>
            </item>
            
            <!-- تدرج خفيف للعمق -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:startColor="#05000000"
                        android:endColor="#10000000"
                        android:angle="90" />
                    <corners android:radius="8dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
</ripple>
