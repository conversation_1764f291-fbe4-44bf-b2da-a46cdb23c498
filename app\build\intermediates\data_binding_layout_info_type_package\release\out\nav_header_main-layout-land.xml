<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="nav_header_main" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout-land\nav_header_main.xml" directory="layout-land" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout-land/nav_header_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="16" endOffset="14"/></Target><Target id="@+id/nav_header_title" view="TextView"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="33"/></Target></Targets></Layout>